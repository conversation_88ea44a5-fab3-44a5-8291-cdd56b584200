<!DOCTYPE html>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <title>Mobile Navigation Example</title>
    <link rel="stylesheet" href="../theme/default/style.css" type="text/css">
    <link rel="stylesheet" href="../theme/default/style.mobile.css" type="text/css" />
    <link rel="stylesheet" href="style.css" type="text/css">
    <script type="text/javascript" src="../lib/OpenLayers.js?mobile"></script>
    <script type="text/javascript" src="mobile-navigation.js"></script>
  </head>
  <body onload="init()">
    <h1 id="title">Mobile Navigation</h1>

    <div id="tags">
        mobile, touch, drag, move, zoom, navigate
    </div>

    <div id="shortdesc">Demonstrate map navigation on mobile</div>

    <div id="map" class="smallmap"></div>
    <div id="docs">

        <p>
            This example demonstrates what OpenLayers provides for map
            navigation on mobile devices.
        </p>

        <p>
            The TouchNavigation control allows to pan the map with touch
            gestures on the screen &ndash; "touchstart", "touchmove",
            "touchend" sequences. It also allows to zoom in with double taps,
            and to zoom out with two-finger single taps. The latter is only
            available on devices supporting multi-touch. Note that in most
            devices Android doesn't support multi-touch in the browser.
        </p>

        <p>
            The Zoom control provides + and - buttons for zooming in and
            out. These buttons should work on any device, and the zoom out
            button is especially needed for devices that don't support
            multi-touch.
        </p> 
        <p>
            See the <a href="mobile-navigation.js" target="_blank">mobile-navigation.js
            source</a> to see how this is done.
        </p> 
    </div>
  </body>
</html>
