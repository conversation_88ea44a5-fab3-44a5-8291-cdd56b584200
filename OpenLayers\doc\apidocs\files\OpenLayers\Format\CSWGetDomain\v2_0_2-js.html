<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0//EN" "http://www.w3.org/TR/REC-html40/strict.dtd">

<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><title>OpenLayers.Format.CSWGetDomain.v2_0_2 - OpenLayers</title><link rel="stylesheet" type="text/css" href="../../../../styles/main.css"><script language=JavaScript src="../../../../javascript/main.js"></script><script language=JavaScript src="../../../../javascript/prettify.js"></script><script language=JavaScript src="../../../../javascript/searchdata.js"></script></head><body class="ContentPage" onLoad="NDOnLoad();prettyPrint();"><script language=JavaScript><!--
if (browserType) {document.write("<div class=" + browserType + ">");if (browserVer) {document.write("<div class=" + browserVer + ">"); }}// --></script>

<!--  Generated by Natural Docs, version 1.51 -->
<!--  http://www.naturaldocs.org  -->

<!-- saved from url=(0026)http://www.naturaldocs.org -->




<div id=Content><div class="CClass"><div class=CTopic id=MainTopic><h1 class=CTitle><a name="OpenLayers.Format.CSWGetDomain.v2_0_2"></a>OpenLayers.<wbr>Format.<wbr>CSWGetDomain.<wbr>v2_0_2</h1><div class=CBody><p>A format for creating CSWGetDomain v2.0.2 transactions.&nbsp; Create a new instance with the <a href="#OpenLayers.Format.CSWGetDomain.v2_0_2.OpenLayers.Format.CSWGetDomain.v2_0_2" class=LConstructor id=link3 onMouseOver="ShowTip(event, 'tt3', 'link3')" onMouseOut="HideTip('tt3')">OpenLayers.Format.CSWGetDomain.v2_0_2</a> constructor.</p><h4 class=CHeading>Inherits from</h4><ul><li><a href="../XML-js.html#OpenLayers.Format.XML" class=LClass id=link4 onMouseOver="ShowTip(event, 'tt4', 'link4')" onMouseOut="HideTip('tt4')">OpenLayers.Format.XML</a></li></ul><!--START_ND_SUMMARY--><div class=Summary><div class=STitle>Summary</div><div class=SBorder><table border=0 cellspacing=0 cellpadding=0 class=STable><tr class="SMain"><td class=SEntry><a href="#OpenLayers.Format.CSWGetDomain.v2_0_2" >OpenLayers.<wbr>Format.<wbr>CSWGetDomain.<wbr>v2_0_2</a></td><td class=SDescription>A format for creating CSWGetDomain v2.0.2 transactions. </td></tr><tr class="SGroup SIndent1"><td class=SEntry><a href="#OpenLayers.Format.CSWGetDomain.v2_0_2.Properties" >Properties</a></td><td class=SDescription></td></tr><tr class="SProperty SIndent2 SMarked"><td class=SEntry><a href="#OpenLayers.Format.CSWGetDomain.v2_0_2.PropertyName" >PropertyName</a></td><td class=SDescription>{String} Value of the csw:PropertyName element, used when writing a GetDomain document.</td></tr><tr class="SProperty SIndent2"><td class=SEntry><a href="#OpenLayers.Format.CSWGetDomain.v2_0_2.ParameterName" >ParameterName</a></td><td class=SDescription>{String} Value of the csw:ParameterName element, used when writing a GetDomain document.</td></tr><tr class="SGroup SIndent1"><td class=SEntry><a href="#OpenLayers.Format.CSWGetDomain.v2_0_2.Constructor" >Constructor</a></td><td class=SDescription></td></tr><tr class="SConstructor SIndent2 SMarked"><td class=SEntry><a href="#OpenLayers.Format.CSWGetDomain.v2_0_2.OpenLayers.Format.CSWGetDomain.v2_0_2" >OpenLayers.<wbr>Format.<wbr>CSWGetDomain.<wbr>v2_0_2</a></td><td class=SDescription>A class for parsing and generating CSWGetDomain v2.0.2 transactions.</td></tr><tr class="SGroup SIndent1"><td class=SEntry><a href="#OpenLayers.Format.CSWGetDomain.v2_0_2.Functions" >Functions</a></td><td class=SDescription></td></tr><tr class="SFunction SIndent2 SMarked"><td class=SEntry><a href="#OpenLayers.Format.CSWGetDomain.v2_0_2.read" id=link1 onMouseOver="ShowTip(event, 'tt1', 'link1')" onMouseOut="HideTip('tt1')">read</a></td><td class=SDescription>Parse the response from a GetDomain request.</td></tr><tr class="SFunction SIndent2"><td class=SEntry><a href="#OpenLayers.Format.CSWGetDomain.v2_0_2.write" id=link2 onMouseOver="ShowTip(event, 'tt2', 'link2')" onMouseOut="HideTip('tt2')">write</a></td><td class=SDescription>Given an configuration js object, write a CSWGetDomain request.</td></tr></table></div></div><!--END_ND_SUMMARY--></div></div></div>

<div class="CGroup"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Format.CSWGetDomain.v2_0_2.Properties"></a>Properties</h3></div></div>

<div class="CProperty"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Format.CSWGetDomain.v2_0_2.PropertyName"></a>PropertyName</h3><div class=CBody><p>{String} Value of the csw:PropertyName element, used when writing a GetDomain document.</p></div></div></div>

<div class="CProperty"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Format.CSWGetDomain.v2_0_2.ParameterName"></a>ParameterName</h3><div class=CBody><p>{String} Value of the csw:ParameterName element, used when writing a GetDomain document.</p></div></div></div>

<div class="CGroup"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Format.CSWGetDomain.v2_0_2.Constructor"></a>Constructor</h3></div></div>

<div class="CConstructor"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Format.CSWGetDomain.v2_0_2.OpenLayers.Format.CSWGetDomain.v2_0_2"></a>OpenLayers.<wbr>Format.<wbr>CSWGetDomain.<wbr>v2_0_2</h3><div class=CBody><p>A class for parsing and generating CSWGetDomain v2.0.2 transactions.</p><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>options</td><td class=CDLDescription>{Object} Optional object whose properties will be set on the instance.</td></tr></table><h4 class=CHeading>Valid options properties</h4><ul><li>PropertyName</li><li>ParameterName</li></ul></div></div></div>

<div class="CGroup"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Format.CSWGetDomain.v2_0_2.Functions"></a>Functions</h3></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Format.CSWGetDomain.v2_0_2.read"></a>read</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>read: function(</td><td class="PParameter  prettyprint " nowrap>data</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote><p>Parse the response from a GetDomain request.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Format.CSWGetDomain.v2_0_2.write"></a>write</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>write: function(</td><td class="PParameter  prettyprint " nowrap>options</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote><p>Given an configuration js object, write a CSWGetDomain request.</p><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>options</td><td class=CDLDescription>{Object} A object mapping the request.</td></tr></table><h4 class=CHeading>Returns</h4><p>{String} A serialized CSWGetDomain request.</p></div></div></div>

</div><!--Content-->


<div id=Footer><a href="http://www.naturaldocs.org">Generated by Natural Docs</a></div><!--Footer-->


<div id=Menu><div class=MTitle>OpenLayers<div class=MSubTitle>JavaScript Mapping Library</div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent35')">OpenLayers</a><div class=MGroupContent id=MGroupContent35><div class=MEntry><div class=MFile><a href="../../../OpenLayers-js.html">OpenLayers</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent1')">BaseTypes</a><div class=MGroupContent id=MGroupContent1><div class=MEntry><div class=MFile><a href="../../BaseTypes-js.html">Base Types</a></div></div><div class=MEntry><div class=MFile><a href="../../BaseTypes/Bounds-js.html">Bounds</a></div></div><div class=MEntry><div class=MFile><a href="../../BaseTypes/Class-js.html">Class</a></div></div><div class=MEntry><div class=MFile><a href="../../BaseTypes/Date-js.html">Date</a></div></div><div class=MEntry><div class=MFile><a href="../../BaseTypes/Element-js.html">Element</a></div></div><div class=MEntry><div class=MFile><a href="../../BaseTypes/LonLat-js.html">LonLat</a></div></div><div class=MEntry><div class=MFile><a href="../../BaseTypes/Pixel-js.html">Pixel</a></div></div><div class=MEntry><div class=MFile><a href="../../BaseTypes/Size-js.html">Size</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent3')">Control</a><div class=MGroupContent id=MGroupContent3><div class=MEntry><div class=MFile><a href="../../Control-js.html">Control</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent2')">Control</a><div class=MGroupContent id=MGroupContent2><div class=MEntry><div class=MFile><a href="../../Control/ArgParser-js.html">ArgParser</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/Attribution-js.html">Attribution</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/Button-js.html">Button</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/CacheRead-js.html">CacheRead</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/CacheWrite-js.html">CacheWrite</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/DragFeature-js.html">DragFeature</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/DragPan-js.html">DragPan</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/DrawFeature-js.html">DrawFeature</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/EditingToolbar-js.html">EditingToolbar</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/Geolocate-js.html">Geolocate</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/GetFeature-js.html">GetFeature</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/Graticule-js.html">Graticule</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/KeyboardDefaults-js.html">KeyboardDefaults</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/LayerSwitcher-js.html">LayerSwitcher</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/Measure-js.html">Measure</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/ModifyFeature-js.html">ModifyFeature</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/MousePosition-js.html">MousePosition</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/Navigation-js.html">Navigation</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/NavigationHistory-js.html">NavigationHistory</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/NavToolbar-js.html">NavToolbar</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/OverviewMap-js.html">OverviewMap</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/Pan-js.html">Pan</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/Panel-js.html">Panel</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/PanPanel-js.html">PanPanel</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/PanZoom-js.html">PanZoom</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/PanZoomBar-js.html">PanZoomBar</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/Permalink-js.html">Permalink</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/PinchZoom-js.html">PinchZoom</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/Scale-js.html">Scale</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/ScaleLine-js.html">ScaleLine</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/SelectFeature-js.html">SelectFeature</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/SLDSelect-js.html">SLDSelect</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/Snapping-js.html">Snapping</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/Split-js.html">Split</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/TouchNavigation-js.html">TouchNavigation</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/TransformFeature-js.html">TransformFeature</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/UTFGrid-js.html">UTFGrid</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/WMSGetFeatureInfo-js.html">WMSGetFeatureInfo</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/WMTSGetFeatureInfo-js.html">WMTSGetFeatureInfo</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/Zoom-js.html">Zoom</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/ZoomBox-js.html">ZoomBox</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/ZoomIn-js.html">ZoomIn</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/ZoomOut-js.html">ZoomOut</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/ZoomPanel-js.html">ZoomPanel</a></div></div><div class=MEntry><div class=MFile><a href="../../Control/ZoomToMaxExtent-js.html">ZoomToMaxExtent</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent4')">Feature</a><div class=MGroupContent id=MGroupContent4><div class=MEntry><div class=MFile><a href="../../Feature-js.html">Feature</a></div></div><div class=MEntry><div class=MFile><a href="../../Feature/Vector-js.html">Vector</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent5')">Filter</a><div class=MGroupContent id=MGroupContent5><div class=MEntry><div class=MFile><a href="../../Filter-js.html">Filter</a></div></div><div class=MEntry><div class=MFile><a href="../../Filter/Comparison-js.html">Comparison</a></div></div><div class=MEntry><div class=MFile><a href="../../Filter/FeatureId-js.html">FeatureId</a></div></div><div class=MEntry><div class=MFile><a href="../../Filter/Function-js.html">Function</a></div></div><div class=MEntry><div class=MFile><a href="../../Filter/Logical-js.html">Logical</a></div></div><div class=MEntry><div class=MFile><a href="../../Filter/Spatial-js.html">Spatial</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent17')">Format</a><div class=MGroupContent id=MGroupContent17><div class=MEntry><div class=MFile><a href="../../Format-js.html">Format</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent6')">Filter</a><div class=MGroupContent id=MGroupContent6><div class=MEntry><div class=MFile><a href="../Filter-js.html">Filter</a></div></div><div class=MEntry><div class=MFile><a href="../Filter/v1-js.html">v1</a></div></div><div class=MEntry><div class=MFile><a href="../Filter/v1_0_0-js.html">v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="../Filter/v1_1_0-js.html">v1_1_0</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent7')">GML</a><div class=MGroupContent id=MGroupContent7><div class=MEntry><div class=MFile><a href="../GML-js.html">GML</a></div></div><div class=MEntry><div class=MFile><a href="../GML/Base-js.html">Base</a></div></div><div class=MEntry><div class=MFile><a href="../GML/v2-js.html">v2</a></div></div><div class=MEntry><div class=MFile><a href="../GML/v3-js.html">v3</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent8')">SLD</a><div class=MGroupContent id=MGroupContent8><div class=MEntry><div class=MFile><a href="../SLD-js.html">SLD</a></div></div><div class=MEntry><div class=MFile><a href="../SLD/v1_0_0_GeoServer-js.html">SLD/<wbr>v1_0_0_GeoServer</a></div></div><div class=MEntry><div class=MFile><a href="../SLD/v1-js.html">v1</a></div></div><div class=MEntry><div class=MFile><a href="../SLD/v1_0_0-js.html">v1_0_0</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent9')">OWSCommon</a><div class=MGroupContent id=MGroupContent9><div class=MEntry><div class=MFile><a href="../OWSCommon-js.html">OWSCommon</a></div></div><div class=MEntry><div class=MFile><a href="../OWSCommon/v1-js.html">v1</a></div></div><div class=MEntry><div class=MFile><a href="../OWSCommon/v1_0_0-js.html">v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="../OWSCommon/v1_1_0-js.html">v1_1_0</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent10')">WFSCapabilities</a><div class=MGroupContent id=MGroupContent10><div class=MEntry><div class=MFile><a href="../WFSCapabilities-js.html">WFSCapabilities</a></div></div><div class=MEntry><div class=MFile><a href="../WFSCapabilities/v1-js.html">v1</a></div></div><div class=MEntry><div class=MFile><a href="../WFSCapabilities/v1_0_0-js.html">v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="../WFSCapabilities/v1_1_0-js.html">v1_1_0</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent11')">WFST</a><div class=MGroupContent id=MGroupContent11><div class=MEntry><div class=MFile><a href="../WFST-js.html">WFST</a></div></div><div class=MEntry><div class=MFile><a href="../WFST/v1-js.html">v1</a></div></div><div class=MEntry><div class=MFile><a href="../WFST/v1_0_0-js.html">v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="../WFST/v1_1_0-js.html">v1_1_0</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent12')">WMC</a><div class=MGroupContent id=MGroupContent12><div class=MEntry><div class=MFile><a href="../WMC-js.html">WMC</a></div></div><div class=MEntry><div class=MFile><a href="../WMC/v1-js.html">v1</a></div></div><div class=MEntry><div class=MFile><a href="../WMC/v1_0_0-js.html">v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="../WMC/v1_1_0-js.html">v1_1_0</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent13')">WMSCapabilities</a><div class=MGroupContent id=MGroupContent13><div class=MEntry><div class=MFile><a href="../WMSCapabilities-js.html">WMSCapabilities</a></div></div><div class=MEntry><div class=MFile><a href="../WMSCapabilities/v1-js.html">v1</a></div></div><div class=MEntry><div class=MFile><a href="../WMSCapabilities/v1_1-js.html">v1_1</a></div></div><div class=MEntry><div class=MFile><a href="../WMSCapabilities/v1_1_0-js.html">v1_1_0</a></div></div><div class=MEntry><div class=MFile><a href="../WMSCapabilities/v1_1_1-js.html">v1_1_1</a></div></div><div class=MEntry><div class=MFile><a href="../WMSCapabilities/v1_3-js.html">v1_3</a></div></div><div class=MEntry><div class=MFile><a href="../WMSCapabilities/v1_3_0-js.html">v1_3_0</a></div></div><div class=MEntry><div class=MFile><a href="../WMSCapabilities/v1_1_1_WMSC-js.html">WMSCapabilities/<wbr>v1_1_1_WMSC</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent14')">WMSDescribeLayer</a><div class=MGroupContent id=MGroupContent14><div class=MEntry><div class=MFile><a href="../WMSDescribeLayer-js.html">WMSDescribeLayer</a></div></div><div class=MEntry><div class=MFile><a href="../WMSDescribeLayer/v1_1-js.html">v1_1</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent16')">Format</a><div class=MGroupContent id=MGroupContent16><div class=MEntry><div class=MFile><a href="../ArcXML-js.html">ArcXML</a></div></div><div class=MEntry><div class=MFile><a href="../ArcXML/Features-js.html">ArcXML.<wbr>Features</a></div></div><div class=MEntry><div class=MFile><a href="../Atom-js.html">Atom</a></div></div><div class=MEntry><div class=MFile><a href="../Context-js.html">Context</a></div></div><div class=MEntry><div class=MFile><a href="../CQL-js.html">CQL</a></div></div><div class=MEntry><div class=MFile><a href="../CSWGetDomain-js.html">CSWGetDomain</a></div></div><div class=MEntry><div class=MFile id=MSelected>CSWGetDomain.<wbr>v2_0_2</div></div><div class=MEntry><div class=MFile><a href="../CSWGetRecords-js.html">CSWGetRecords</a></div></div><div class=MEntry><div class=MFile><a href="../CSWGetRecords/v2_0_2-js.html">CSWGetRecords.<wbr>v2_0_2</a></div></div><div class=MEntry><div class=MFile><a href="../EncodedPolyline-js.html">EncodedPolyline</a></div></div><div class=MEntry><div class=MFile><a href="../GeoJSON-js.html">GeoJSON</a></div></div><div class=MEntry><div class=MFile><a href="../GeoRSS-js.html">GeoRSS</a></div></div><div class=MEntry><div class=MFile><a href="../GPX-js.html">GPX</a></div></div><div class=MEntry><div class=MFile><a href="../JSON-js.html">JSON</a></div></div><div class=MEntry><div class=MFile><a href="../KML-js.html">KML</a></div></div><div class=MEntry><div class=MFile><a href="../OGCExceptionReport-js.html">OGCExceptionReport</a></div></div><div class=MEntry><div class=MFile><a href="../OSM-js.html">OSM</a></div></div><div class=MEntry><div class=MFile><a href="../OWSContext-js.html">OWSContext</a></div></div><div class=MEntry><div class=MFile><a href="../OWSContext/v0_3_1-js.html">OWSContext.<wbr>v0_3_1</a></div></div><div class=MEntry><div class=MFile><a href="../QueryStringFilter-js.html">QueryStringFilter</a></div></div><div class=MEntry><div class=MFile><a href="../SOSCapabilities-js.html">SOSCapabilities</a></div></div><div class=MEntry><div class=MFile><a href="../SOSCapabilities/v1_0_0-js.html">SOSCapabilities.<wbr>v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="../SOSGetFeatureOfInterest-js.html">SOSGetFeatureOfInterest</a></div></div><div class=MEntry><div class=MFile><a href="../SOSGetObservation-js.html">SOSGetObservation</a></div></div><div class=MEntry><div class=MFile><a href="../Text-js.html">Text</a></div></div><div class=MEntry><div class=MFile><a href="../WCSCapabilities-js.html">WCSCapabilities</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent15')">WCSCapabilities</a><div class=MGroupContent id=MGroupContent15><div class=MEntry><div class=MFile><a href="../WCSCapabilities/v1-js.html">WCSCapabilities.v1</a></div></div><div class=MEntry><div class=MFile><a href="../WCSCapabilities/v1_0_0-js.html">WCSCapabilities/<wbr>v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="../WCSCapabilities/v1_1_0-js.html">WCSCapabilities/<wbr>v1_1_0</a></div></div></div></div></div><div class=MEntry><div class=MFile><a href="../WCSGetCoverage-js.html">WCSGetCoverage version 1.1.0</a></div></div><div class=MEntry><div class=MFile><a href="../WFS-js.html">WFS</a></div></div><div class=MEntry><div class=MFile><a href="../WFSDescribeFeatureType-js.html">WFSDescribeFeatureType</a></div></div><div class=MEntry><div class=MFile><a href="../WKT-js.html">WKT</a></div></div><div class=MEntry><div class=MFile><a href="../WMSGetFeatureInfo-js.html">WMSGetFeatureInfo</a></div></div><div class=MEntry><div class=MFile><a href="../WMTSCapabilities-js.html">WMTSCapabilities</a></div></div><div class=MEntry><div class=MFile><a href="../WMTSCapabilities/v1_0_0-js.html">WMTSCapabilities.<wbr>v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="../WPSCapabilities-js.html">WPSCapabilities</a></div></div><div class=MEntry><div class=MFile><a href="../WPSCapabilities/v1_0_0-js.html">WPSCapabilities.<wbr>v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="../WPSDescribeProcess-js.html">WPSDescribeProcess</a></div></div><div class=MEntry><div class=MFile><a href="../WPSExecute-js.html">WPSExecute version 1.0.0</a></div></div><div class=MEntry><div class=MFile><a href="../XLS-js.html">XLS</a></div></div><div class=MEntry><div class=MFile><a href="../XLS/v1-js.html">XLS.v1</a></div></div><div class=MEntry><div class=MFile><a href="../XLS/v1_1_0-js.html">XLS.<wbr>v1_1_0</a></div></div><div class=MEntry><div class=MFile><a href="../XML-js.html">XML</a></div></div><div class=MEntry><div class=MFile><a href="../XML/VersionedOGC-js.html">XML.<wbr>VersionedOGC</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent18')">Geometry</a><div class=MGroupContent id=MGroupContent18><div class=MEntry><div class=MFile><a href="../../Geometry-js.html">Geometry</a></div></div><div class=MEntry><div class=MFile><a href="../../Geometry/Collection-js.html">Collection</a></div></div><div class=MEntry><div class=MFile><a href="../../Geometry/Curve-js.html">Curve</a></div></div><div class=MEntry><div class=MFile><a href="../../Geometry/LinearRing-js.html">LinearRing</a></div></div><div class=MEntry><div class=MFile><a href="../../Geometry/LineString-js.html">LineString</a></div></div><div class=MEntry><div class=MFile><a href="../../Geometry/MultiLineString-js.html">MultiLineString</a></div></div><div class=MEntry><div class=MFile><a href="../../Geometry/MultiPoint-js.html">MultiPoint</a></div></div><div class=MEntry><div class=MFile><a href="../../Geometry/MultiPolygon-js.html">MultiPolygon</a></div></div><div class=MEntry><div class=MFile><a href="../../Geometry/Point-js.html">Point</a></div></div><div class=MEntry><div class=MFile><a href="../../Geometry/Polygon-js.html">Polygon</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent19')">Handler</a><div class=MGroupContent id=MGroupContent19><div class=MEntry><div class=MFile><a href="../../Handler-js.html">Handler</a></div></div><div class=MEntry><div class=MFile><a href="../../Handler/Box-js.html">Box</a></div></div><div class=MEntry><div class=MFile><a href="../../Handler/Click-js.html">Click</a></div></div><div class=MEntry><div class=MFile><a href="../../Handler/Drag-js.html">Drag</a></div></div><div class=MEntry><div class=MFile><a href="../../Handler/Feature-js.html">Feature</a></div></div><div class=MEntry><div class=MFile><a href="../../Handler/Hover-js.html">Hover</a></div></div><div class=MEntry><div class=MFile><a href="../../Handler/Keyboard-js.html">Keyboard</a></div></div><div class=MEntry><div class=MFile><a href="../../Handler/MouseWheel-js.html">MouseWheel</a></div></div><div class=MEntry><div class=MFile><a href="../../Handler/Path-js.html">Path</a></div></div><div class=MEntry><div class=MFile><a href="../../Handler/Pinch-js.html">Pinch</a></div></div><div class=MEntry><div class=MFile><a href="../../Handler/Point-js.html">Point</a></div></div><div class=MEntry><div class=MFile><a href="../../Handler/Polygon-js.html">Polygon</a></div></div><div class=MEntry><div class=MFile><a href="../../Handler/RegularPolygon-js.html">RegularPolygon</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent21')">Lang</a><div class=MGroupContent id=MGroupContent21><div class=MEntry><div class=MFile><a href="../../Lang-js.html">Lang</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent20')">Lang</a><div class=MGroupContent id=MGroupContent20><div class=MEntry><div class=MFile><a href="../../Lang/ar-js.html">ar</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/be-tarask-js.html">be-tarask</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/bg-js.html">bg</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/br-js.html">br</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/ca-js.html">ca</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/cs-CZ-js.html">cs-CZ</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/da-DK-js.html">da-DK</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/de-js.html">de</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/en-js.html">en</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/en-CA-js.html">en-CA</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/es-js.html">es</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/el-js.html">el</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/fi-js.html">fi</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/fr-js.html">fr</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/fur-js.html">fur</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/gl-js.html">gl</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/gsw-js.html">gsw</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/hr-js.html">hr</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/hsb-js.html">hsb</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/hu-js.html">hu</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/ia-js.html">ia</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/id-js.html">id</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/io-js.html">io</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/is-js.html">is</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/it-js.html">it</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/ja-js.html">ja</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/km-js.html">km</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/ksh-js.html">ksh</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/lt-js.html">lt</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/nds-js.html">nds</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/nb-js.html">nb</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/nl-js.html">nl</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/nn-js.html">nn</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/oc-js.html">oc</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/pt-js.html">pt</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/pl-js.html">pl</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/pt-BR-js.html">pt-BR</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/ru-js.html">ru</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/sk-js.html">sk</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/sv-SE-js.html">sv-SE</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/te-js.html">te</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/vi-js.html">vi</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/zh-CN-js.html">zh-CN</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/zh-TW-js.html">zh-TW</a></div></div><div class=MEntry><div class=MFile><a href="../../Lang/ro-js.html">Lang[&ldquo;ro&rdquo;]</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent23')">Layer</a><div class=MGroupContent id=MGroupContent23><div class=MEntry><div class=MFile><a href="../../Layer-js.html">Layer</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent22')">Layer</a><div class=MGroupContent id=MGroupContent22><div class=MEntry><div class=MFile><a href="../../Layer/ArcGISCache-js.html">ArcGISCache.js</a></div></div><div class=MEntry><div class=MFile><a href="../../Layer/ArcGIS93Rest-js.html">ArcGIS93Rest</a></div></div><div class=MEntry><div class=MFile><a href="../../Layer/ArcIMS-js.html">ArcIMS</a></div></div><div class=MEntry><div class=MFile><a href="../../Layer/Bing-js.html">Bing</a></div></div><div class=MEntry><div class=MFile><a href="../../Layer/Boxes-js.html">Boxes</a></div></div><div class=MEntry><div class=MFile><a href="../../Layer/EventPane-js.html">EventPane</a></div></div><div class=MEntry><div class=MFile><a href="../../Layer/FixedZoomLevels-js.html">FixedZoomLevels</a></div></div><div class=MEntry><div class=MFile><a href="../../Layer/GeoRSS-js.html">GeoRSS</a></div></div><div class=MEntry><div class=MFile><a href="../../Layer/Google-js.html">Google</a></div></div><div class=MEntry><div class=MFile><a href="../../Layer/Google/v3-js.html">Google.v3</a></div></div><div class=MEntry><div class=MFile><a href="../../Layer/Grid-js.html">Grid</a></div></div><div class=MEntry><div class=MFile><a href="../../Layer/HTTPRequest-js.html">HTTPRequest</a></div></div><div class=MEntry><div class=MFile><a href="../../Layer/Image-js.html">Image</a></div></div><div class=MEntry><div class=MFile><a href="../../Layer/KaMap-js.html">KaMap</a></div></div><div class=MEntry><div class=MFile><a href="../../Layer/KaMapCache-js.html">KaMapCache</a></div></div><div class=MEntry><div class=MFile><a href="../../Layer/MapGuide-js.html">MapGuide</a></div></div><div class=MEntry><div class=MFile><a href="../../Layer/MapServer-js.html">MapServer</a></div></div><div class=MEntry><div class=MFile><a href="../../Layer/Markers-js.html">Markers</a></div></div><div class=MEntry><div class=MFile><a href="../../Layer/OSM-js.html">OSM</a></div></div><div class=MEntry><div class=MFile><a href="../../Layer/PointGrid-js.html">PointGrid</a></div></div><div class=MEntry><div class=MFile><a href="../../Layer/PointTrack-js.html">PointTrack</a></div></div><div class=MEntry><div class=MFile><a href="../../Layer/SphericalMercator-js.html">SphericalMercator</a></div></div><div class=MEntry><div class=MFile><a href="../../Layer/Text-js.html">Text</a></div></div><div class=MEntry><div class=MFile><a href="../../Layer/TileCache-js.html">TileCache</a></div></div><div class=MEntry><div class=MFile><a href="../../Layer/TMS-js.html">TMS</a></div></div><div class=MEntry><div class=MFile><a href="../../Layer/Vector-js.html">Vector</a></div></div><div class=MEntry><div class=MFile><a href="../../Layer/Vector/RootContainer-js.html">Vector.<wbr>RootContainer</a></div></div><div class=MEntry><div class=MFile><a href="../../Layer/WMS-js.html">WMS</a></div></div><div class=MEntry><div class=MFile><a href="../../Layer/WMTS-js.html">WMTS</a></div></div><div class=MEntry><div class=MFile><a href="../../Layer/WorldWind-js.html">WorldWind</a></div></div><div class=MEntry><div class=MFile><a href="../../Layer/XYZ-js.html">XYZ</a></div></div><div class=MEntry><div class=MFile><a href="../../Layer/Zoomify-js.html">Zoomify</a></div></div><div class=MEntry><div class=MFile><a href="../../Layer/UTFGrid-js.html">UTFGrid</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent24')">Marker</a><div class=MGroupContent id=MGroupContent24><div class=MEntry><div class=MFile><a href="../../Marker-js.html">Marker</a></div></div><div class=MEntry><div class=MFile><a href="../../Marker/Box-js.html">Box</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent25')">Popup</a><div class=MGroupContent id=MGroupContent25><div class=MEntry><div class=MFile><a href="../../Popup-js.html">Popup</a></div></div><div class=MEntry><div class=MFile><a href="../../Popup/Anchored-js.html">Anchored</a></div></div><div class=MEntry><div class=MFile><a href="../../Popup/Framed-js.html">Framed</a></div></div><div class=MEntry><div class=MFile><a href="../../Popup/FramedCloud-js.html">FramedCloud</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent28')">Protocol</a><div class=MGroupContent id=MGroupContent28><div class=MEntry><div class=MFile><a href="../../Protocol-js.html">Protocol</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent26')">Protocol</a><div class=MGroupContent id=MGroupContent26><div class=MEntry><div class=MFile><a href="../../Protocol/CSW-js.html">CSW</a></div></div><div class=MEntry><div class=MFile><a href="../../Protocol/CSW/v2_0_2-js.html">CSW.<wbr>v2_0_2</a></div></div><div class=MEntry><div class=MFile><a href="../../Protocol/HTTP-js.html">HTTP</a></div></div><div class=MEntry><div class=MFile><a href="../../Protocol/Script-js.html">Script</a></div></div><div class=MEntry><div class=MFile><a href="../../Protocol/SOS-js.html">SOS.<wbr>DEFAULTS</a></div></div><div class=MEntry><div class=MFile><a href="../../Protocol/SOS/v1_0_0-js.html">SOS.<wbr>v1_0_0</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent27')">WFS</a><div class=MGroupContent id=MGroupContent27><div class=MEntry><div class=MFile><a href="../../Protocol/WFS-js.html">WFS</a></div></div><div class=MEntry><div class=MFile><a href="../../Protocol/WFS/v1-js.html">v1</a></div></div><div class=MEntry><div class=MFile><a href="../../Protocol/WFS/v1_0_0-js.html">v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="../../Protocol/WFS/v1_1_0-js.html">v1_1_0</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent29')">Renderer</a><div class=MGroupContent id=MGroupContent29><div class=MEntry><div class=MFile><a href="../../Renderer-js.html">Renderer</a></div></div><div class=MEntry><div class=MFile><a href="../../Renderer/Canvas-js.html">Canvas</a></div></div><div class=MEntry><div class=MFile><a href="../../Renderer/Elements-js.html">ElementsIndexer</a></div></div><div class=MEntry><div class=MFile><a href="../../Renderer/SVG-js.html">SVG</a></div></div><div class=MEntry><div class=MFile><a href="../../Renderer/VML-js.html">VML</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent30')">Request</a><div class=MGroupContent id=MGroupContent30><div class=MEntry><div class=MFile><a href="../../Request-js.html">Request</a></div></div><div class=MEntry><div class=MFile><a href="../../Request/XMLHttpRequest-js.html">XMLHttpRequest</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent31')">Strategy</a><div class=MGroupContent id=MGroupContent31><div class=MEntry><div class=MFile><a href="../../Strategy-js.html">Strategy</a></div></div><div class=MEntry><div class=MFile><a href="../../Strategy/BBOX-js.html">BBOX</a></div></div><div class=MEntry><div class=MFile><a href="../../Strategy/Cluster-js.html">Cluster</a></div></div><div class=MEntry><div class=MFile><a href="../../Strategy/Filter-js.html">Filter</a></div></div><div class=MEntry><div class=MFile><a href="../../Strategy/Fixed-js.html">Fixed</a></div></div><div class=MEntry><div class=MFile><a href="../../Strategy/Paging-js.html">Paging</a></div></div><div class=MEntry><div class=MFile><a href="../../Strategy/Refresh-js.html">Refresh</a></div></div><div class=MEntry><div class=MFile><a href="../../Strategy/Save-js.html">Save</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent32')">Symbolizer</a><div class=MGroupContent id=MGroupContent32><div class=MEntry><div class=MFile><a href="../../Symbolizer-js.html">Symbolizer</a></div></div><div class=MEntry><div class=MFile><a href="../../Symbolizer/Line-js.html">Line</a></div></div><div class=MEntry><div class=MFile><a href="../../Symbolizer/Point-js.html">Point</a></div></div><div class=MEntry><div class=MFile><a href="../../Symbolizer/Polygon-js.html">Polygon</a></div></div><div class=MEntry><div class=MFile><a href="../../Symbolizer/Raster-js.html">Raster</a></div></div><div class=MEntry><div class=MFile><a href="../../Symbolizer/Text-js.html">Text</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent33')">Tile</a><div class=MGroupContent id=MGroupContent33><div class=MEntry><div class=MFile><a href="../../Tile-js.html">Tile</a></div></div><div class=MEntry><div class=MFile><a href="../../Tile/Image-js.html">Image</a></div></div><div class=MEntry><div class=MFile><a href="../../Tile/Image/IFrame-js.html">Image.<wbr>IFrame</a></div></div><div class=MEntry><div class=MFile><a href="../../Tile/UTFGrid-js.html">UTFGrid</a></div></div></div></div></div><div class=MEntry><div class=MFile><a href="../../../deprecated-js.html">Deprecated</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent34')">OpenLayers</a><div class=MGroupContent id=MGroupContent34><div class=MEntry><div class=MFile><a href="../../Console-js.html">Console</a></div></div><div class=MEntry><div class=MFile><a href="../../Events-js.html">Events</a></div></div><div class=MEntry><div class=MFile><a href="../../Icon-js.html">Icon</a></div></div><div class=MEntry><div class=MFile><a href="../../Map-js.html">Map</a></div></div><div class=MEntry><div class=MFile><a href="../../Animation-js.html">OpenLayers.<wbr>Animation</a></div></div><div class=MEntry><div class=MFile><a href="../../Events/buttonclick-js.html">OpenLayers.<wbr>Events.<wbr>buttonclick</a></div></div><div class=MEntry><div class=MFile><a href="../../Events/featureclick-js.html">OpenLayers.<wbr>Events.<wbr>featureclick</a></div></div><div class=MEntry><div class=MFile><a href="../../Kinetic-js.html">OpenLayers.<wbr>Kinetic</a></div></div><div class=MEntry><div class=MFile><a href="../../TileManager-js.html">OpenLayers.<wbr>TileManager</a></div></div><div class=MEntry><div class=MFile><a href="../../Util/vendorPrefix-js.html">OpenLayers.<wbr>Util.<wbr>vendorPrefix</a></div></div><div class=MEntry><div class=MFile><a href="../../WPSClient-js.html">OpenLayers.<wbr>WPSClient</a></div></div><div class=MEntry><div class=MFile><a href="../../WPSProcess-js.html">OpenLayers.<wbr>WPSProcess</a></div></div><div class=MEntry><div class=MFile><a href="../../Projection-js.html">Projection</a></div></div><div class=MEntry><div class=MFile><a href="../../Rule-js.html">Rule</a></div></div><div class=MEntry><div class=MFile><a href="../../SingleFile-js.html">SingleFile.js</a></div></div><div class=MEntry><div class=MFile><a href="../../Spherical-js.html">Spherical</a></div></div><div class=MEntry><div class=MFile><a href="../../Style-js.html">Style</a></div></div><div class=MEntry><div class=MFile><a href="../../Style2-js.html">Style2</a></div></div><div class=MEntry><div class=MFile><a href="../../StyleMap-js.html">StyleMap</a></div></div><div class=MEntry><div class=MFile><a href="../../Tween-js.html">Tween</a></div></div><div class=MEntry><div class=MFile><a href="../../Util-js.html">Util</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent36')">Index</a><div class=MGroupContent id=MGroupContent36><div class=MEntry><div class=MIndex><a href="../../../../index/General.html">Everything</a></div></div><div class=MEntry><div class=MIndex><a href="../../../../index/Classes.html">Classes</a></div></div><div class=MEntry><div class=MIndex><a href="../../../../index/Constants.html">Constants</a></div></div><div class=MEntry><div class=MIndex><a href="../../../../index/Functions.html">Functions</a></div></div><div class=MEntry><div class=MIndex><a href="../../../../index/Properties.html">Properties</a></div></div><div class=MEntry><div class=MIndex><a href="../../../../index/Files.html">Files</a></div></div><div class=MEntry><div class=MIndex><a href="../../../../index/Constructor.html">Constructor</a></div></div></div></div></div><script type="text/javascript"><!--
var searchPanel = new SearchPanel("searchPanel", "HTML", "../../../../search");
--></script><div id=MSearchPanel class=MSearchPanelInactive><input type=text id=MSearchField value=Search onFocus="searchPanel.OnSearchFieldFocus(true)" onBlur="searchPanel.OnSearchFieldFocus(false)" onKeyUp="searchPanel.OnSearchFieldChange()"><select id=MSearchType onFocus="searchPanel.OnSearchTypeFocus(true)" onBlur="searchPanel.OnSearchTypeFocus(false)" onChange="searchPanel.OnSearchTypeChange()"><option  id=MSearchEverything selected value="General">Everything</option><option value="Classes">Classes</option><option value="Constants">Constants</option><option value="Constructor">Constructor</option><option value="Files">Files</option><option value="Functions">Functions</option><option value="Properties">Properties</option></select></div><script language=JavaScript><!--
HideAllBut([16, 17, 35], 37);// --></script></div><!--Menu-->



<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt1"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>read: function(</td><td class="PParameter  prettyprint " nowrap>data</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Parse the response from a GetDomain request.</div></div><div class=CToolTip id="tt2"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>write: function(</td><td class="PParameter  prettyprint " nowrap>options</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Given an configuration js object, write a CSWGetDomain request.</div></div><div class=CToolTip id="tt3"><div class=CConstructor>A class for parsing and generating CSWGetDomain v2.0.2 transactions.</div></div><div class=CToolTip id="tt4"><div class=CClass>Read and write XML. </div></div><!--END_ND_TOOLTIPS-->




<div id=MSearchResultsWindow><iframe src="" frameborder=0 name=MSearchResults id=MSearchResults></iframe><a href="javascript:searchPanel.CloseResultsWindow()" id=MSearchResultsWindowClose>Close</a></div>


<script language=JavaScript><!--
if (browserType) {if (browserVer) {document.write("</div>"); }document.write("</div>");}// --></script></body></html>