<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0//EN" "http://www.w3.org/TR/REC-html40/strict.dtd">

<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><title>OpenLayers.Geometry - OpenLayers</title><link rel="stylesheet" type="text/css" href="../../styles/main.css"><script language=JavaScript src="../../javascript/main.js"></script><script language=JavaScript src="../../javascript/prettify.js"></script><script language=JavaScript src="../../javascript/searchdata.js"></script></head><body class="ContentPage" onLoad="NDOnLoad();prettyPrint();"><script language=JavaScript><!--
if (browserType) {document.write("<div class=" + browserType + ">");if (browserVer) {document.write("<div class=" + browserVer + ">"); }}// --></script>

<!--  Generated by Natural Docs, version 1.51 -->
<!--  http://www.naturaldocs.org  -->

<!-- saved from url=(0026)http://www.naturaldocs.org -->




<div id=Content><div class="CClass"><div class=CTopic id=MainTopic><h1 class=CTitle><a name="OpenLayers.Geometry"></a>OpenLayers.<wbr>Geometry</h1><div class=CBody><p>A Geometry is a description of a geographic object.&nbsp;  Create an instance of this class with the <a href="#OpenLayers.Geometry.OpenLayers.Geometry" class=LConstructor id=link21 onMouseOver="ShowTip(event, 'tt1', 'link21')" onMouseOut="HideTip('tt1')">OpenLayers.Geometry</a> constructor.&nbsp;  This is a base class, typical geometry types are described by subclasses of this class.</p><p>Note that if you use the <a href="#OpenLayers.Geometry.OpenLayers.Geometry.fromWKT" class=LFunction id=link22 onMouseOver="ShowTip(event, 'tt17', 'link22')" onMouseOut="HideTip('tt17')">OpenLayers.Geometry.fromWKT</a> method, you must explicitly include the OpenLayers.Format.WKT in your build.</p><!--START_ND_SUMMARY--><div class=Summary><div class=STitle>Summary</div><div class=SBorder><table border=0 cellspacing=0 cellpadding=0 class=STable><tr class="SMain"><td class=SEntry><a href="#OpenLayers.Geometry" >OpenLayers.<wbr>Geometry</a></td><td class=SDescription>A Geometry is a description of a geographic object. </td></tr><tr class="SGroup SIndent1"><td class=SEntry><a href="#OpenLayers.Geometry.Properties" >Properties</a></td><td class=SDescription></td></tr><tr class="SProperty SIndent2 SMarked"><td class=SEntry><a href="#OpenLayers.Geometry.id" >id</a></td><td class=SDescription>{String} A unique identifier for this geometry.</td></tr><tr class="SProperty SIndent2"><td class=SEntry><a href="#OpenLayers.Geometry.parent" >parent</a></td><td class=SDescription>{<a href="#OpenLayers.Geometry.OpenLayers.Geometry" class=LConstructor id=link1 onMouseOver="ShowTip(event, 'tt1', 'link1')" onMouseOut="HideTip('tt1')">OpenLayers.Geometry</a>}This is set when a Geometry is added as component of another geometry</td></tr><tr class="SProperty SIndent2 SMarked"><td class=SEntry><a href="#OpenLayers.Geometry.bounds" >bounds</a></td><td class=SDescription>{<a href="BaseTypes/Bounds-js.html#OpenLayers.Bounds" class=LClass id=link2 onMouseOver="ShowTip(event, 'tt2', 'link2')" onMouseOut="HideTip('tt2')">OpenLayers.Bounds</a>} The bounds of this geometry</td></tr><tr class="SGroup SIndent1"><td class=SEntry><a href="#OpenLayers.Geometry.Constructor" >Constructor</a></td><td class=SDescription></td></tr><tr class="SConstructor SIndent2 SMarked"><td class=SEntry><a href="#OpenLayers.Geometry.OpenLayers.Geometry" >OpenLayers.<wbr>Geometry</a></td><td class=SDescription>Creates a geometry object.</td></tr><tr class="SGroup SIndent1"><td class=SEntry><a href="#OpenLayers.Geometry.Functions" >Functions</a></td><td class=SDescription></td></tr><tr class="SFunction SIndent2 SMarked"><td class=SEntry><a href="#OpenLayers.Geometry.destroy" id=link3 onMouseOver="ShowTip(event, 'tt3', 'link3')" onMouseOut="HideTip('tt3')">destroy</a></td><td class=SDescription>Destroy this geometry.</td></tr><tr class="SFunction SIndent2"><td class=SEntry><a href="#OpenLayers.Geometry.clone" id=link4 onMouseOver="ShowTip(event, 'tt4', 'link4')" onMouseOut="HideTip('tt4')">clone</a></td><td class=SDescription>Create a clone of this geometry. </td></tr><tr class="SFunction SIndent2 SMarked"><td class=SEntry><a href="#OpenLayers.Geometry.setBounds" id=link5 onMouseOver="ShowTip(event, 'tt5', 'link5')" onMouseOut="HideTip('tt5')">setBounds</a></td><td class=SDescription>Set the bounds for this Geometry.</td></tr><tr class="SFunction SIndent2"><td class=SEntry><a href="#OpenLayers.Geometry.clearBounds" id=link6 onMouseOver="ShowTip(event, 'tt6', 'link6')" onMouseOut="HideTip('tt6')">clearBounds</a></td><td class=SDescription>Nullify this components bounds and that of its parent as well.</td></tr><tr class="SFunction SIndent2 SMarked"><td class=SEntry><a href="#OpenLayers.Geometry.extendBounds" id=link7 onMouseOver="ShowTip(event, 'tt7', 'link7')" onMouseOut="HideTip('tt7')">extendBounds</a></td><td class=SDescription>Extend the existing bounds to include the new bounds. </td></tr><tr class="SFunction SIndent2"><td class=SEntry><a href="#OpenLayers.Geometry.getBounds" id=link8 onMouseOver="ShowTip(event, 'tt8', 'link8')" onMouseOut="HideTip('tt8')">getBounds</a></td><td class=SDescription>Get the bounds for this Geometry. </td></tr><tr class="SFunction SIndent2 SMarked"><td class=SEntry><a href="#OpenLayers.Geometry.calculateBounds" id=link9 onMouseOver="ShowTip(event, 'tt9', 'link9')" onMouseOut="HideTip('tt9')">calculateBounds</a></td><td class=SDescription>Recalculate the bounds for the geometry.</td></tr><tr class="SFunction SIndent2"><td class=SEntry><a href="#OpenLayers.Geometry.distanceTo" id=link10 onMouseOver="ShowTip(event, 'tt10', 'link10')" onMouseOut="HideTip('tt10')">distanceTo</a></td><td class=SDescription>Calculate the closest distance between two geometries (on the x-y plane).</td></tr><tr class="SFunction SIndent2 SMarked"><td class=SEntry><a href="#OpenLayers.Geometry.getVertices" id=link11 onMouseOver="ShowTip(event, 'tt11', 'link11')" onMouseOut="HideTip('tt11')">getVertices</a></td><td class=SDescription>Return a list of all points in this geometry.</td></tr><tr class="SFunction SIndent2"><td class=SEntry><a href="#OpenLayers.Geometry.atPoint" id=link12 onMouseOver="ShowTip(event, 'tt12', 'link12')" onMouseOut="HideTip('tt12')">atPoint</a></td><td class=SDescription></td></tr><tr class="SFunction SIndent2 SMarked"><td class=SEntry><a href="#OpenLayers.Geometry.getLength" id=link13 onMouseOver="ShowTip(event, 'tt13', 'link13')" onMouseOut="HideTip('tt13')">getLength</a></td><td class=SDescription>Calculate the length of this geometry. </td></tr><tr class="SFunction SIndent2"><td class=SEntry><a href="#OpenLayers.Geometry.getArea" id=link14 onMouseOver="ShowTip(event, 'tt14', 'link14')" onMouseOut="HideTip('tt14')">getArea</a></td><td class=SDescription>Calculate the area of this geometry. </td></tr><tr class="SFunction SIndent2 SMarked"><td class=SEntry><a href="#OpenLayers.Geometry.getCentroid" id=link15 onMouseOver="ShowTip(event, 'tt15', 'link15')" onMouseOut="HideTip('tt15')">getCentroid</a></td><td class=SDescription>Calculate the centroid of this geometry. </td></tr><tr class="SFunction SIndent2"><td class=SEntry><a href="#OpenLayers.Geometry.toString" id=link16 onMouseOver="ShowTip(event, 'tt16', 'link16')" onMouseOut="HideTip('tt16')">toString</a></td><td class=SDescription>Returns a text representation of the geometry. </td></tr><tr class="SFunction SIndent2 SMarked"><td class=SEntry><a href="#OpenLayers.Geometry.OpenLayers.Geometry.fromWKT" id=link17 onMouseOver="ShowTip(event, 'tt17', 'link17')" onMouseOut="HideTip('tt17')">OpenLayers.<wbr>Geometry.<wbr>fromWKT</a></td><td class=SDescription>Generate a geometry given a Well-Known Text string. </td></tr><tr class="SFunction SIndent2"><td class=SEntry><a href="#OpenLayers.Geometry.OpenLayers.Geometry.segmentsIntersect" id=link18 onMouseOver="ShowTip(event, 'tt18', 'link18')" onMouseOut="HideTip('tt18')">OpenLayers.<wbr>Geometry.<wbr>segmentsIntersect</a></td><td class=SDescription>Determine whether two line segments intersect. </td></tr><tr class="SFunction SIndent2 SMarked"><td class=SEntry><a href="#OpenLayers.Geometry.OpenLayers.Geometry.distanceToSegment" id=link19 onMouseOver="ShowTip(event, 'tt19', 'link19')" onMouseOut="HideTip('tt19')">OpenLayers.<wbr>Geometry.<wbr>distanceToSegment</a></td><td class=SDescription></td></tr><tr class="SFunction SIndent2"><td class=SEntry><a href="#OpenLayers.Geometry.OpenLayers.Geometry.distanceSquaredToSegment" id=link20 onMouseOver="ShowTip(event, 'tt20', 'link20')" onMouseOut="HideTip('tt20')">OpenLayers.<wbr>Geometry.<wbr>distanceSquaredToSegment</a></td><td class=SDescription>Usually the distanceToSegment function should be used. </td></tr></table></div></div><!--END_ND_SUMMARY--></div></div></div>

<div class="CGroup"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Geometry.Properties"></a>Properties</h3></div></div>

<div class="CProperty"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Geometry.id"></a>id</h3><div class=CBody><p>{String} A unique identifier for this geometry.</p></div></div></div>

<div class="CProperty"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Geometry.parent"></a>parent</h3><div class=CBody><p>{<a href="#OpenLayers.Geometry.OpenLayers.Geometry" class=LConstructor id=link23 onMouseOver="ShowTip(event, 'tt1', 'link23')" onMouseOut="HideTip('tt1')">OpenLayers.Geometry</a>}This is set when a Geometry is added as component of another geometry</p></div></div></div>

<div class="CProperty"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Geometry.bounds"></a>bounds</h3><div class=CBody><p>{<a href="BaseTypes/Bounds-js.html#OpenLayers.Bounds" class=LClass id=link24 onMouseOver="ShowTip(event, 'tt2', 'link24')" onMouseOut="HideTip('tt2')">OpenLayers.Bounds</a>} The bounds of this geometry</p></div></div></div>

<div class="CGroup"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Geometry.Constructor"></a>Constructor</h3></div></div>

<div class="CConstructor"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Geometry.OpenLayers.Geometry"></a>OpenLayers.<wbr>Geometry</h3><div class=CBody><p>Creates a geometry object.</p></div></div></div>

<div class="CGroup"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Geometry.Functions"></a>Functions</h3></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Geometry.destroy"></a>destroy</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">destroy: function()</td></tr></table></blockquote><p>Destroy this geometry.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Geometry.clone"></a>clone</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">clone: function()</td></tr></table></blockquote><p>Create a clone of this geometry.&nbsp;  Does not set any non-standard properties of the cloned geometry.</p><h4 class=CHeading>Returns</h4><p>{<a href="#OpenLayers.Geometry.OpenLayers.Geometry" class=LConstructor id=link25 onMouseOver="ShowTip(event, 'tt1', 'link25')" onMouseOut="HideTip('tt1')">OpenLayers.Geometry</a>} An exact clone of this geometry.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Geometry.setBounds"></a>setBounds</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>setBounds: function(</td><td class="PParameter  prettyprint " nowrap>bounds</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote><p>Set the bounds for this Geometry.</p><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>bounds</td><td class=CDLDescription>{<a href="BaseTypes/Bounds-js.html#OpenLayers.Bounds" class=LClass id=link26 onMouseOver="ShowTip(event, 'tt2', 'link26')" onMouseOut="HideTip('tt2')">OpenLayers.Bounds</a>}</td></tr></table></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Geometry.clearBounds"></a>clearBounds</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">clearBounds: function()</td></tr></table></blockquote><p>Nullify this components bounds and that of its parent as well.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Geometry.extendBounds"></a>extendBounds</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>extendBounds: function(</td><td class="PParameter  prettyprint " nowrap>newBounds</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote><p>Extend the existing bounds to include the new bounds.&nbsp; If geometry&rsquo;s bounds is not yet set, then set a new Bounds.</p><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>newBounds</td><td class=CDLDescription>{<a href="BaseTypes/Bounds-js.html#OpenLayers.Bounds" class=LClass id=link27 onMouseOver="ShowTip(event, 'tt2', 'link27')" onMouseOut="HideTip('tt2')">OpenLayers.Bounds</a>}</td></tr></table></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Geometry.getBounds"></a>getBounds</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">getBounds: function()</td></tr></table></blockquote><p>Get the bounds for this Geometry.&nbsp; If bounds is not set, it is calculated again, this makes queries faster.</p><h4 class=CHeading>Returns</h4><p>{<a href="BaseTypes/Bounds-js.html#OpenLayers.Bounds" class=LClass id=link28 onMouseOver="ShowTip(event, 'tt2', 'link28')" onMouseOut="HideTip('tt2')">OpenLayers.Bounds</a>}</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Geometry.calculateBounds"></a>calculateBounds</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">calculateBounds: function()</td></tr></table></blockquote><p>Recalculate the bounds for the geometry.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Geometry.distanceTo"></a>distanceTo</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>distanceTo: function(</td><td class="PParameter  prettyprint " nowrap>geometry,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>options</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote><p>Calculate the closest distance between two geometries (on the x-y plane).</p><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>geometry</td><td class=CDLDescription>{<a href="#OpenLayers.Geometry.OpenLayers.Geometry" class=LConstructor id=link29 onMouseOver="ShowTip(event, 'tt1', 'link29')" onMouseOut="HideTip('tt1')">OpenLayers.Geometry</a>} The target geometry.</td></tr><tr><td class=CDLEntry>options</td><td class=CDLDescription>{Object} Optional properties for configuring the distance calculation.</td></tr></table><p>Valid options depend on the specific geometry type.</p><h4 class=CHeading>Returns</h4><p>{Number | Object} The distance between this geometry and the target.&nbsp; If details is true, the return will be an object with distance, x0, y0, x1, and x2 properties.&nbsp;  The x0 and y0 properties represent the coordinates of the closest point on this geometry.&nbsp; The x1 and y1 properties represent the coordinates of the closest point on the target geometry.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Geometry.getVertices"></a>getVertices</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>getVertices: function(</td><td class="PParameter  prettyprint " nowrap>nodes</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote><p>Return a list of all points in this geometry.</p><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>nodes</td><td class=CDLDescription>{Boolean} For lines, only return vertices that are endpoints.&nbsp;  If false, for lines, only vertices that are not endpoints will be returned.&nbsp;  If not provided, all vertices will be returned.</td></tr></table><h4 class=CHeading>Returns</h4><p>{Array} A list of all vertices in the geometry.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Geometry.atPoint"></a>atPoint</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>atPoint: function(</td><td class="PParameter  prettyprint " nowrap>lonlat,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>toleranceLon,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>toleranceLat</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>Note</td><td class=CDLDescription>This is only an approximation based on the bounds of the geometry.</td></tr></table><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>lonlat</td><td class=CDLDescription>{&lt;OpenLayers.LonLat&gt;|Object} OpenLayers.LonLat or an object with a &lsquo;lon&rsquo; and &lsquo;lat&rsquo; properties.</td></tr><tr><td class=CDLEntry>toleranceLon</td><td class=CDLDescription>{float} Optional tolerance in Geometric Coords</td></tr><tr><td class=CDLEntry>toleranceLat</td><td class=CDLDescription>{float} Optional tolerance in Geographic Coords</td></tr></table><h4 class=CHeading>Returns</h4><p>{Boolean} Whether or not the geometry is at the specified location</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Geometry.getLength"></a>getLength</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">getLength: function()</td></tr></table></blockquote><p>Calculate the length of this geometry.&nbsp; This method is defined in subclasses.</p><h4 class=CHeading>Returns</h4><p>{Float} The length of the collection by summing its parts</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Geometry.getArea"></a>getArea</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">getArea: function()</td></tr></table></blockquote><p>Calculate the area of this geometry.&nbsp; This method is defined in subclasses.</p><h4 class=CHeading>Returns</h4><p>{Float} The area of the collection by summing its parts</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Geometry.getCentroid"></a>getCentroid</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">getCentroid: function()</td></tr></table></blockquote><p>Calculate the centroid of this geometry.&nbsp; This method is defined in subclasses.</p><h4 class=CHeading>Returns</h4><p>{<a href="Geometry/Point-js.html#OpenLayers.Geometry.Point" class=LClass id=link30 onMouseOver="ShowTip(event, 'tt21', 'link30')" onMouseOut="HideTip('tt21')">OpenLayers.Geometry.Point</a>} The centroid of the collection</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Geometry.toString"></a>toString</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">toString: function()</td></tr></table></blockquote><p>Returns a text representation of the geometry.&nbsp;  If the WKT format is included in a build, this will be the Well-Known Text representation.</p><h4 class=CHeading>Returns</h4><p>{String} String representation of this geometry.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Geometry.OpenLayers.Geometry.fromWKT"></a>OpenLayers.<wbr>Geometry.<wbr>fromWKT</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>OpenLayers.Geometry.fromWKT = function(</td><td class="PParameter  prettyprint " nowrap>wkt</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote><p>Generate a geometry given a Well-Known Text string.&nbsp;  For this method to work, you must include the OpenLayers.Format.WKT in your build explicitly.</p><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>wkt</td><td class=CDLDescription>{String} A string representing the geometry in Well-Known Text.</td></tr></table><h4 class=CHeading>Returns</h4><p>{<a href="#OpenLayers.Geometry.OpenLayers.Geometry" class=LConstructor id=link31 onMouseOver="ShowTip(event, 'tt1', 'link31')" onMouseOut="HideTip('tt1')">OpenLayers.Geometry</a>} A geometry of the appropriate class.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Geometry.OpenLayers.Geometry.segmentsIntersect"></a>OpenLayers.<wbr>Geometry.<wbr>segmentsIntersect</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>OpenLayers.Geometry.segmentsIntersect = function(</td><td class="PParameter  prettyprint " nowrap>seg1,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>seg2,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>options</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote><p>Determine whether two line segments intersect.&nbsp;  Optionally calculates and returns the intersection point.&nbsp;  This function is optimized for cases where seg1.x2 &gt;= seg2.x1 || seg2.x2 &gt;= seg1.x1.&nbsp;  In those obvious cases where there is no intersection, the function should not be called.</p><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>seg1</td><td class=CDLDescription>{Object} Object representing a segment with properties x1, y1, x2, and y2.&nbsp;  The start point is represented by x1 and y1.&nbsp;  The end point is represented by x2 and y2.&nbsp;  Start and end are ordered so that x1 &lt; x2.</td></tr><tr><td class=CDLEntry>seg2</td><td class=CDLDescription>{Object} Object representing a segment with properties x1, y1, x2, and y2.&nbsp;  The start point is represented by x1 and y1.&nbsp;  The end point is represented by x2 and y2.&nbsp;  Start and end are ordered so that x1 &lt; x2.</td></tr><tr><td class=CDLEntry>options</td><td class=CDLDescription>{Object} Optional properties for calculating the intersection.</td></tr></table><h4 class=CHeading>Valid options</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>point</td><td class=CDLDescription>{Boolean} Return the intersection point.&nbsp;  If false, the actual intersection point will not be calculated.&nbsp;  If true and the segments intersect, the intersection point will be returned.&nbsp;  If true and the segments do not intersect, false will be returned.&nbsp;  If true and the segments are coincident, true will be returned.</td></tr><tr><td class=CDLEntry>tolerance</td><td class=CDLDescription>{Number} If a non-null value is provided, if the segments are within the tolerance distance, this will be considered an intersection.&nbsp; In addition, if the point option is true and the calculated intersection is within the tolerance distance of an end point, the endpoint will be returned instead of the calculated intersection.&nbsp;  Further, if the intersection is within the tolerance of endpoints on both segments, or if two segment endpoints are within the tolerance distance of eachother (but no intersection is otherwise calculated), an endpoint on the first segment provided will be returned.</td></tr></table><h4 class=CHeading>Returns</h4><p>{Boolean | <a href="Geometry/Point-js.html#OpenLayers.Geometry.Point" class=LClass id=link32 onMouseOver="ShowTip(event, 'tt21', 'link32')" onMouseOut="HideTip('tt21')">OpenLayers.Geometry.Point</a>}  The two segments intersect.&nbsp; If the point argument is true, the return will be the intersection point or false if none exists.&nbsp;  If point is true and the segments are coincident, return will be true (and the instersection is equal to the shorter segment).</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Geometry.OpenLayers.Geometry.distanceToSegment"></a>OpenLayers.<wbr>Geometry.<wbr>distanceToSegment</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>OpenLayers.Geometry.distanceToSegment = function(</td><td class="PParameter  prettyprint " nowrap>point,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>segment</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>point</td><td class=CDLDescription>{Object} An object with x and y properties representing the point coordinates.</td></tr><tr><td class=CDLEntry>segment</td><td class=CDLDescription>{Object} An object with x1, y1, x2, and y2 properties representing endpoint coordinates.</td></tr></table><h4 class=CHeading>Returns</h4><p>{Object} An object with distance, along, x, and y properties.&nbsp;  The distance will be the shortest distance between the input point and segment.&nbsp; The x and y properties represent the coordinates along the segment where the shortest distance meets the segment.&nbsp; The along attribute describes how far between the two segment points the given point is.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Geometry.OpenLayers.Geometry.distanceSquaredToSegment"></a>OpenLayers.<wbr>Geometry.<wbr>distanceSquaredToSegment</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>OpenLayers.Geometry.distanceSquaredToSegment = function(</td><td class="PParameter  prettyprint " nowrap>point,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>segment</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote><p>Usually the distanceToSegment function should be used.&nbsp; This variant however can be used for comparisons where the exact distance is not important.</p><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>point</td><td class=CDLDescription>{Object} An object with x and y properties representing the point coordinates.</td></tr><tr><td class=CDLEntry>segment</td><td class=CDLDescription>{Object} An object with x1, y1, x2, and y2 properties representing endpoint coordinates.</td></tr></table><h4 class=CHeading>Returns</h4><p>{Object} An object with squared distance, along, x, and y properties.&nbsp; The distance will be the shortest distance between the input point and segment.&nbsp; The x and y properties represent the coordinates along the segment where the shortest distance meets the segment.&nbsp; The along attribute describes how far between the two segment points the given point is.</p></div></div></div>

</div><!--Content-->


<div id=Footer><a href="http://www.naturaldocs.org">Generated by Natural Docs</a></div><!--Footer-->


<div id=Menu><div class=MTitle>OpenLayers<div class=MSubTitle>JavaScript Mapping Library</div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent35')">OpenLayers</a><div class=MGroupContent id=MGroupContent35><div class=MEntry><div class=MFile><a href="../OpenLayers-js.html">OpenLayers</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent1')">BaseTypes</a><div class=MGroupContent id=MGroupContent1><div class=MEntry><div class=MFile><a href="BaseTypes-js.html">Base Types</a></div></div><div class=MEntry><div class=MFile><a href="BaseTypes/Bounds-js.html">Bounds</a></div></div><div class=MEntry><div class=MFile><a href="BaseTypes/Class-js.html">Class</a></div></div><div class=MEntry><div class=MFile><a href="BaseTypes/Date-js.html">Date</a></div></div><div class=MEntry><div class=MFile><a href="BaseTypes/Element-js.html">Element</a></div></div><div class=MEntry><div class=MFile><a href="BaseTypes/LonLat-js.html">LonLat</a></div></div><div class=MEntry><div class=MFile><a href="BaseTypes/Pixel-js.html">Pixel</a></div></div><div class=MEntry><div class=MFile><a href="BaseTypes/Size-js.html">Size</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent3')">Control</a><div class=MGroupContent id=MGroupContent3><div class=MEntry><div class=MFile><a href="Control-js.html">Control</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent2')">Control</a><div class=MGroupContent id=MGroupContent2><div class=MEntry><div class=MFile><a href="Control/ArgParser-js.html">ArgParser</a></div></div><div class=MEntry><div class=MFile><a href="Control/Attribution-js.html">Attribution</a></div></div><div class=MEntry><div class=MFile><a href="Control/Button-js.html">Button</a></div></div><div class=MEntry><div class=MFile><a href="Control/CacheRead-js.html">CacheRead</a></div></div><div class=MEntry><div class=MFile><a href="Control/CacheWrite-js.html">CacheWrite</a></div></div><div class=MEntry><div class=MFile><a href="Control/DragFeature-js.html">DragFeature</a></div></div><div class=MEntry><div class=MFile><a href="Control/DragPan-js.html">DragPan</a></div></div><div class=MEntry><div class=MFile><a href="Control/DrawFeature-js.html">DrawFeature</a></div></div><div class=MEntry><div class=MFile><a href="Control/EditingToolbar-js.html">EditingToolbar</a></div></div><div class=MEntry><div class=MFile><a href="Control/Geolocate-js.html">Geolocate</a></div></div><div class=MEntry><div class=MFile><a href="Control/GetFeature-js.html">GetFeature</a></div></div><div class=MEntry><div class=MFile><a href="Control/Graticule-js.html">Graticule</a></div></div><div class=MEntry><div class=MFile><a href="Control/KeyboardDefaults-js.html">KeyboardDefaults</a></div></div><div class=MEntry><div class=MFile><a href="Control/LayerSwitcher-js.html">LayerSwitcher</a></div></div><div class=MEntry><div class=MFile><a href="Control/Measure-js.html">Measure</a></div></div><div class=MEntry><div class=MFile><a href="Control/ModifyFeature-js.html">ModifyFeature</a></div></div><div class=MEntry><div class=MFile><a href="Control/MousePosition-js.html">MousePosition</a></div></div><div class=MEntry><div class=MFile><a href="Control/Navigation-js.html">Navigation</a></div></div><div class=MEntry><div class=MFile><a href="Control/NavigationHistory-js.html">NavigationHistory</a></div></div><div class=MEntry><div class=MFile><a href="Control/NavToolbar-js.html">NavToolbar</a></div></div><div class=MEntry><div class=MFile><a href="Control/OverviewMap-js.html">OverviewMap</a></div></div><div class=MEntry><div class=MFile><a href="Control/Pan-js.html">Pan</a></div></div><div class=MEntry><div class=MFile><a href="Control/Panel-js.html">Panel</a></div></div><div class=MEntry><div class=MFile><a href="Control/PanPanel-js.html">PanPanel</a></div></div><div class=MEntry><div class=MFile><a href="Control/PanZoom-js.html">PanZoom</a></div></div><div class=MEntry><div class=MFile><a href="Control/PanZoomBar-js.html">PanZoomBar</a></div></div><div class=MEntry><div class=MFile><a href="Control/Permalink-js.html">Permalink</a></div></div><div class=MEntry><div class=MFile><a href="Control/PinchZoom-js.html">PinchZoom</a></div></div><div class=MEntry><div class=MFile><a href="Control/Scale-js.html">Scale</a></div></div><div class=MEntry><div class=MFile><a href="Control/ScaleLine-js.html">ScaleLine</a></div></div><div class=MEntry><div class=MFile><a href="Control/SelectFeature-js.html">SelectFeature</a></div></div><div class=MEntry><div class=MFile><a href="Control/SLDSelect-js.html">SLDSelect</a></div></div><div class=MEntry><div class=MFile><a href="Control/Snapping-js.html">Snapping</a></div></div><div class=MEntry><div class=MFile><a href="Control/Split-js.html">Split</a></div></div><div class=MEntry><div class=MFile><a href="Control/TouchNavigation-js.html">TouchNavigation</a></div></div><div class=MEntry><div class=MFile><a href="Control/TransformFeature-js.html">TransformFeature</a></div></div><div class=MEntry><div class=MFile><a href="Control/UTFGrid-js.html">UTFGrid</a></div></div><div class=MEntry><div class=MFile><a href="Control/WMSGetFeatureInfo-js.html">WMSGetFeatureInfo</a></div></div><div class=MEntry><div class=MFile><a href="Control/WMTSGetFeatureInfo-js.html">WMTSGetFeatureInfo</a></div></div><div class=MEntry><div class=MFile><a href="Control/Zoom-js.html">Zoom</a></div></div><div class=MEntry><div class=MFile><a href="Control/ZoomBox-js.html">ZoomBox</a></div></div><div class=MEntry><div class=MFile><a href="Control/ZoomIn-js.html">ZoomIn</a></div></div><div class=MEntry><div class=MFile><a href="Control/ZoomOut-js.html">ZoomOut</a></div></div><div class=MEntry><div class=MFile><a href="Control/ZoomPanel-js.html">ZoomPanel</a></div></div><div class=MEntry><div class=MFile><a href="Control/ZoomToMaxExtent-js.html">ZoomToMaxExtent</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent4')">Feature</a><div class=MGroupContent id=MGroupContent4><div class=MEntry><div class=MFile><a href="Feature-js.html">Feature</a></div></div><div class=MEntry><div class=MFile><a href="Feature/Vector-js.html">Vector</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent5')">Filter</a><div class=MGroupContent id=MGroupContent5><div class=MEntry><div class=MFile><a href="Filter-js.html">Filter</a></div></div><div class=MEntry><div class=MFile><a href="Filter/Comparison-js.html">Comparison</a></div></div><div class=MEntry><div class=MFile><a href="Filter/FeatureId-js.html">FeatureId</a></div></div><div class=MEntry><div class=MFile><a href="Filter/Function-js.html">Function</a></div></div><div class=MEntry><div class=MFile><a href="Filter/Logical-js.html">Logical</a></div></div><div class=MEntry><div class=MFile><a href="Filter/Spatial-js.html">Spatial</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent17')">Format</a><div class=MGroupContent id=MGroupContent17><div class=MEntry><div class=MFile><a href="Format-js.html">Format</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent6')">Filter</a><div class=MGroupContent id=MGroupContent6><div class=MEntry><div class=MFile><a href="Format/Filter-js.html">Filter</a></div></div><div class=MEntry><div class=MFile><a href="Format/Filter/v1-js.html">v1</a></div></div><div class=MEntry><div class=MFile><a href="Format/Filter/v1_0_0-js.html">v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="Format/Filter/v1_1_0-js.html">v1_1_0</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent7')">GML</a><div class=MGroupContent id=MGroupContent7><div class=MEntry><div class=MFile><a href="Format/GML-js.html">GML</a></div></div><div class=MEntry><div class=MFile><a href="Format/GML/Base-js.html">Base</a></div></div><div class=MEntry><div class=MFile><a href="Format/GML/v2-js.html">v2</a></div></div><div class=MEntry><div class=MFile><a href="Format/GML/v3-js.html">v3</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent8')">SLD</a><div class=MGroupContent id=MGroupContent8><div class=MEntry><div class=MFile><a href="Format/SLD-js.html">SLD</a></div></div><div class=MEntry><div class=MFile><a href="Format/SLD/v1_0_0_GeoServer-js.html">SLD/<wbr>v1_0_0_GeoServer</a></div></div><div class=MEntry><div class=MFile><a href="Format/SLD/v1-js.html">v1</a></div></div><div class=MEntry><div class=MFile><a href="Format/SLD/v1_0_0-js.html">v1_0_0</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent9')">OWSCommon</a><div class=MGroupContent id=MGroupContent9><div class=MEntry><div class=MFile><a href="Format/OWSCommon-js.html">OWSCommon</a></div></div><div class=MEntry><div class=MFile><a href="Format/OWSCommon/v1-js.html">v1</a></div></div><div class=MEntry><div class=MFile><a href="Format/OWSCommon/v1_0_0-js.html">v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="Format/OWSCommon/v1_1_0-js.html">v1_1_0</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent10')">WFSCapabilities</a><div class=MGroupContent id=MGroupContent10><div class=MEntry><div class=MFile><a href="Format/WFSCapabilities-js.html">WFSCapabilities</a></div></div><div class=MEntry><div class=MFile><a href="Format/WFSCapabilities/v1-js.html">v1</a></div></div><div class=MEntry><div class=MFile><a href="Format/WFSCapabilities/v1_0_0-js.html">v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="Format/WFSCapabilities/v1_1_0-js.html">v1_1_0</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent11')">WFST</a><div class=MGroupContent id=MGroupContent11><div class=MEntry><div class=MFile><a href="Format/WFST-js.html">WFST</a></div></div><div class=MEntry><div class=MFile><a href="Format/WFST/v1-js.html">v1</a></div></div><div class=MEntry><div class=MFile><a href="Format/WFST/v1_0_0-js.html">v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="Format/WFST/v1_1_0-js.html">v1_1_0</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent12')">WMC</a><div class=MGroupContent id=MGroupContent12><div class=MEntry><div class=MFile><a href="Format/WMC-js.html">WMC</a></div></div><div class=MEntry><div class=MFile><a href="Format/WMC/v1-js.html">v1</a></div></div><div class=MEntry><div class=MFile><a href="Format/WMC/v1_0_0-js.html">v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="Format/WMC/v1_1_0-js.html">v1_1_0</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent13')">WMSCapabilities</a><div class=MGroupContent id=MGroupContent13><div class=MEntry><div class=MFile><a href="Format/WMSCapabilities-js.html">WMSCapabilities</a></div></div><div class=MEntry><div class=MFile><a href="Format/WMSCapabilities/v1-js.html">v1</a></div></div><div class=MEntry><div class=MFile><a href="Format/WMSCapabilities/v1_1-js.html">v1_1</a></div></div><div class=MEntry><div class=MFile><a href="Format/WMSCapabilities/v1_1_0-js.html">v1_1_0</a></div></div><div class=MEntry><div class=MFile><a href="Format/WMSCapabilities/v1_1_1-js.html">v1_1_1</a></div></div><div class=MEntry><div class=MFile><a href="Format/WMSCapabilities/v1_3-js.html">v1_3</a></div></div><div class=MEntry><div class=MFile><a href="Format/WMSCapabilities/v1_3_0-js.html">v1_3_0</a></div></div><div class=MEntry><div class=MFile><a href="Format/WMSCapabilities/v1_1_1_WMSC-js.html">WMSCapabilities/<wbr>v1_1_1_WMSC</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent14')">WMSDescribeLayer</a><div class=MGroupContent id=MGroupContent14><div class=MEntry><div class=MFile><a href="Format/WMSDescribeLayer-js.html">WMSDescribeLayer</a></div></div><div class=MEntry><div class=MFile><a href="Format/WMSDescribeLayer/v1_1-js.html">v1_1</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent16')">Format</a><div class=MGroupContent id=MGroupContent16><div class=MEntry><div class=MFile><a href="Format/ArcXML-js.html">ArcXML</a></div></div><div class=MEntry><div class=MFile><a href="Format/ArcXML/Features-js.html">ArcXML.<wbr>Features</a></div></div><div class=MEntry><div class=MFile><a href="Format/Atom-js.html">Atom</a></div></div><div class=MEntry><div class=MFile><a href="Format/Context-js.html">Context</a></div></div><div class=MEntry><div class=MFile><a href="Format/CQL-js.html">CQL</a></div></div><div class=MEntry><div class=MFile><a href="Format/CSWGetDomain-js.html">CSWGetDomain</a></div></div><div class=MEntry><div class=MFile><a href="Format/CSWGetDomain/v2_0_2-js.html">CSWGetDomain.<wbr>v2_0_2</a></div></div><div class=MEntry><div class=MFile><a href="Format/CSWGetRecords-js.html">CSWGetRecords</a></div></div><div class=MEntry><div class=MFile><a href="Format/CSWGetRecords/v2_0_2-js.html">CSWGetRecords.<wbr>v2_0_2</a></div></div><div class=MEntry><div class=MFile><a href="Format/EncodedPolyline-js.html">EncodedPolyline</a></div></div><div class=MEntry><div class=MFile><a href="Format/GeoJSON-js.html">GeoJSON</a></div></div><div class=MEntry><div class=MFile><a href="Format/GeoRSS-js.html">GeoRSS</a></div></div><div class=MEntry><div class=MFile><a href="Format/GPX-js.html">GPX</a></div></div><div class=MEntry><div class=MFile><a href="Format/JSON-js.html">JSON</a></div></div><div class=MEntry><div class=MFile><a href="Format/KML-js.html">KML</a></div></div><div class=MEntry><div class=MFile><a href="Format/OGCExceptionReport-js.html">OGCExceptionReport</a></div></div><div class=MEntry><div class=MFile><a href="Format/OSM-js.html">OSM</a></div></div><div class=MEntry><div class=MFile><a href="Format/OWSContext-js.html">OWSContext</a></div></div><div class=MEntry><div class=MFile><a href="Format/OWSContext/v0_3_1-js.html">OWSContext.<wbr>v0_3_1</a></div></div><div class=MEntry><div class=MFile><a href="Format/QueryStringFilter-js.html">QueryStringFilter</a></div></div><div class=MEntry><div class=MFile><a href="Format/SOSCapabilities-js.html">SOSCapabilities</a></div></div><div class=MEntry><div class=MFile><a href="Format/SOSCapabilities/v1_0_0-js.html">SOSCapabilities.<wbr>v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="Format/SOSGetFeatureOfInterest-js.html">SOSGetFeatureOfInterest</a></div></div><div class=MEntry><div class=MFile><a href="Format/SOSGetObservation-js.html">SOSGetObservation</a></div></div><div class=MEntry><div class=MFile><a href="Format/Text-js.html">Text</a></div></div><div class=MEntry><div class=MFile><a href="Format/WCSCapabilities-js.html">WCSCapabilities</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent15')">WCSCapabilities</a><div class=MGroupContent id=MGroupContent15><div class=MEntry><div class=MFile><a href="Format/WCSCapabilities/v1-js.html">WCSCapabilities.v1</a></div></div><div class=MEntry><div class=MFile><a href="Format/WCSCapabilities/v1_0_0-js.html">WCSCapabilities/<wbr>v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="Format/WCSCapabilities/v1_1_0-js.html">WCSCapabilities/<wbr>v1_1_0</a></div></div></div></div></div><div class=MEntry><div class=MFile><a href="Format/WCSGetCoverage-js.html">WCSGetCoverage version 1.1.0</a></div></div><div class=MEntry><div class=MFile><a href="Format/WFS-js.html">WFS</a></div></div><div class=MEntry><div class=MFile><a href="Format/WFSDescribeFeatureType-js.html">WFSDescribeFeatureType</a></div></div><div class=MEntry><div class=MFile><a href="Format/WKT-js.html">WKT</a></div></div><div class=MEntry><div class=MFile><a href="Format/WMSGetFeatureInfo-js.html">WMSGetFeatureInfo</a></div></div><div class=MEntry><div class=MFile><a href="Format/WMTSCapabilities-js.html">WMTSCapabilities</a></div></div><div class=MEntry><div class=MFile><a href="Format/WMTSCapabilities/v1_0_0-js.html">WMTSCapabilities.<wbr>v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="Format/WPSCapabilities-js.html">WPSCapabilities</a></div></div><div class=MEntry><div class=MFile><a href="Format/WPSCapabilities/v1_0_0-js.html">WPSCapabilities.<wbr>v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="Format/WPSDescribeProcess-js.html">WPSDescribeProcess</a></div></div><div class=MEntry><div class=MFile><a href="Format/WPSExecute-js.html">WPSExecute version 1.0.0</a></div></div><div class=MEntry><div class=MFile><a href="Format/XLS-js.html">XLS</a></div></div><div class=MEntry><div class=MFile><a href="Format/XLS/v1-js.html">XLS.v1</a></div></div><div class=MEntry><div class=MFile><a href="Format/XLS/v1_1_0-js.html">XLS.<wbr>v1_1_0</a></div></div><div class=MEntry><div class=MFile><a href="Format/XML-js.html">XML</a></div></div><div class=MEntry><div class=MFile><a href="Format/XML/VersionedOGC-js.html">XML.<wbr>VersionedOGC</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent18')">Geometry</a><div class=MGroupContent id=MGroupContent18><div class=MEntry><div class=MFile id=MSelected>Geometry</div></div><div class=MEntry><div class=MFile><a href="Geometry/Collection-js.html">Collection</a></div></div><div class=MEntry><div class=MFile><a href="Geometry/Curve-js.html">Curve</a></div></div><div class=MEntry><div class=MFile><a href="Geometry/LinearRing-js.html">LinearRing</a></div></div><div class=MEntry><div class=MFile><a href="Geometry/LineString-js.html">LineString</a></div></div><div class=MEntry><div class=MFile><a href="Geometry/MultiLineString-js.html">MultiLineString</a></div></div><div class=MEntry><div class=MFile><a href="Geometry/MultiPoint-js.html">MultiPoint</a></div></div><div class=MEntry><div class=MFile><a href="Geometry/MultiPolygon-js.html">MultiPolygon</a></div></div><div class=MEntry><div class=MFile><a href="Geometry/Point-js.html">Point</a></div></div><div class=MEntry><div class=MFile><a href="Geometry/Polygon-js.html">Polygon</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent19')">Handler</a><div class=MGroupContent id=MGroupContent19><div class=MEntry><div class=MFile><a href="Handler-js.html">Handler</a></div></div><div class=MEntry><div class=MFile><a href="Handler/Box-js.html">Box</a></div></div><div class=MEntry><div class=MFile><a href="Handler/Click-js.html">Click</a></div></div><div class=MEntry><div class=MFile><a href="Handler/Drag-js.html">Drag</a></div></div><div class=MEntry><div class=MFile><a href="Handler/Feature-js.html">Feature</a></div></div><div class=MEntry><div class=MFile><a href="Handler/Hover-js.html">Hover</a></div></div><div class=MEntry><div class=MFile><a href="Handler/Keyboard-js.html">Keyboard</a></div></div><div class=MEntry><div class=MFile><a href="Handler/MouseWheel-js.html">MouseWheel</a></div></div><div class=MEntry><div class=MFile><a href="Handler/Path-js.html">Path</a></div></div><div class=MEntry><div class=MFile><a href="Handler/Pinch-js.html">Pinch</a></div></div><div class=MEntry><div class=MFile><a href="Handler/Point-js.html">Point</a></div></div><div class=MEntry><div class=MFile><a href="Handler/Polygon-js.html">Polygon</a></div></div><div class=MEntry><div class=MFile><a href="Handler/RegularPolygon-js.html">RegularPolygon</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent21')">Lang</a><div class=MGroupContent id=MGroupContent21><div class=MEntry><div class=MFile><a href="Lang-js.html">Lang</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent20')">Lang</a><div class=MGroupContent id=MGroupContent20><div class=MEntry><div class=MFile><a href="Lang/ar-js.html">ar</a></div></div><div class=MEntry><div class=MFile><a href="Lang/be-tarask-js.html">be-tarask</a></div></div><div class=MEntry><div class=MFile><a href="Lang/bg-js.html">bg</a></div></div><div class=MEntry><div class=MFile><a href="Lang/br-js.html">br</a></div></div><div class=MEntry><div class=MFile><a href="Lang/ca-js.html">ca</a></div></div><div class=MEntry><div class=MFile><a href="Lang/cs-CZ-js.html">cs-CZ</a></div></div><div class=MEntry><div class=MFile><a href="Lang/da-DK-js.html">da-DK</a></div></div><div class=MEntry><div class=MFile><a href="Lang/de-js.html">de</a></div></div><div class=MEntry><div class=MFile><a href="Lang/en-js.html">en</a></div></div><div class=MEntry><div class=MFile><a href="Lang/en-CA-js.html">en-CA</a></div></div><div class=MEntry><div class=MFile><a href="Lang/es-js.html">es</a></div></div><div class=MEntry><div class=MFile><a href="Lang/el-js.html">el</a></div></div><div class=MEntry><div class=MFile><a href="Lang/fi-js.html">fi</a></div></div><div class=MEntry><div class=MFile><a href="Lang/fr-js.html">fr</a></div></div><div class=MEntry><div class=MFile><a href="Lang/fur-js.html">fur</a></div></div><div class=MEntry><div class=MFile><a href="Lang/gl-js.html">gl</a></div></div><div class=MEntry><div class=MFile><a href="Lang/gsw-js.html">gsw</a></div></div><div class=MEntry><div class=MFile><a href="Lang/hr-js.html">hr</a></div></div><div class=MEntry><div class=MFile><a href="Lang/hsb-js.html">hsb</a></div></div><div class=MEntry><div class=MFile><a href="Lang/hu-js.html">hu</a></div></div><div class=MEntry><div class=MFile><a href="Lang/ia-js.html">ia</a></div></div><div class=MEntry><div class=MFile><a href="Lang/id-js.html">id</a></div></div><div class=MEntry><div class=MFile><a href="Lang/io-js.html">io</a></div></div><div class=MEntry><div class=MFile><a href="Lang/is-js.html">is</a></div></div><div class=MEntry><div class=MFile><a href="Lang/it-js.html">it</a></div></div><div class=MEntry><div class=MFile><a href="Lang/ja-js.html">ja</a></div></div><div class=MEntry><div class=MFile><a href="Lang/km-js.html">km</a></div></div><div class=MEntry><div class=MFile><a href="Lang/ksh-js.html">ksh</a></div></div><div class=MEntry><div class=MFile><a href="Lang/lt-js.html">lt</a></div></div><div class=MEntry><div class=MFile><a href="Lang/nds-js.html">nds</a></div></div><div class=MEntry><div class=MFile><a href="Lang/nb-js.html">nb</a></div></div><div class=MEntry><div class=MFile><a href="Lang/nl-js.html">nl</a></div></div><div class=MEntry><div class=MFile><a href="Lang/nn-js.html">nn</a></div></div><div class=MEntry><div class=MFile><a href="Lang/oc-js.html">oc</a></div></div><div class=MEntry><div class=MFile><a href="Lang/pl-js.html">pl</a></div></div><div class=MEntry><div class=MFile><a href="Lang/pt-js.html">pt</a></div></div><div class=MEntry><div class=MFile><a href="Lang/pt-BR-js.html">pt-BR</a></div></div><div class=MEntry><div class=MFile><a href="Lang/ru-js.html">ru</a></div></div><div class=MEntry><div class=MFile><a href="Lang/sk-js.html">sk</a></div></div><div class=MEntry><div class=MFile><a href="Lang/sv-SE-js.html">sv-SE</a></div></div><div class=MEntry><div class=MFile><a href="Lang/te-js.html">te</a></div></div><div class=MEntry><div class=MFile><a href="Lang/vi-js.html">vi</a></div></div><div class=MEntry><div class=MFile><a href="Lang/zh-CN-js.html">zh-CN</a></div></div><div class=MEntry><div class=MFile><a href="Lang/zh-TW-js.html">zh-TW</a></div></div><div class=MEntry><div class=MFile><a href="Lang/ro-js.html">Lang[&ldquo;ro&rdquo;]</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent23')">Layer</a><div class=MGroupContent id=MGroupContent23><div class=MEntry><div class=MFile><a href="Layer-js.html">Layer</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent22')">Layer</a><div class=MGroupContent id=MGroupContent22><div class=MEntry><div class=MFile><a href="Layer/ArcGISCache-js.html">ArcGISCache.js</a></div></div><div class=MEntry><div class=MFile><a href="Layer/ArcGIS93Rest-js.html">ArcGIS93Rest</a></div></div><div class=MEntry><div class=MFile><a href="Layer/ArcIMS-js.html">ArcIMS</a></div></div><div class=MEntry><div class=MFile><a href="Layer/Bing-js.html">Bing</a></div></div><div class=MEntry><div class=MFile><a href="Layer/Boxes-js.html">Boxes</a></div></div><div class=MEntry><div class=MFile><a href="Layer/EventPane-js.html">EventPane</a></div></div><div class=MEntry><div class=MFile><a href="Layer/FixedZoomLevels-js.html">FixedZoomLevels</a></div></div><div class=MEntry><div class=MFile><a href="Layer/GeoRSS-js.html">GeoRSS</a></div></div><div class=MEntry><div class=MFile><a href="Layer/Google-js.html">Google</a></div></div><div class=MEntry><div class=MFile><a href="Layer/Google/v3-js.html">Google.v3</a></div></div><div class=MEntry><div class=MFile><a href="Layer/Grid-js.html">Grid</a></div></div><div class=MEntry><div class=MFile><a href="Layer/HTTPRequest-js.html">HTTPRequest</a></div></div><div class=MEntry><div class=MFile><a href="Layer/Image-js.html">Image</a></div></div><div class=MEntry><div class=MFile><a href="Layer/KaMap-js.html">KaMap</a></div></div><div class=MEntry><div class=MFile><a href="Layer/KaMapCache-js.html">KaMapCache</a></div></div><div class=MEntry><div class=MFile><a href="Layer/MapGuide-js.html">MapGuide</a></div></div><div class=MEntry><div class=MFile><a href="Layer/MapServer-js.html">MapServer</a></div></div><div class=MEntry><div class=MFile><a href="Layer/Markers-js.html">Markers</a></div></div><div class=MEntry><div class=MFile><a href="Layer/PointGrid-js.html">PointGrid</a></div></div><div class=MEntry><div class=MFile><a href="Layer/PointTrack-js.html">PointTrack</a></div></div><div class=MEntry><div class=MFile><a href="Layer/SphericalMercator-js.html">SphericalMercator</a></div></div><div class=MEntry><div class=MFile><a href="Layer/Text-js.html">Text</a></div></div><div class=MEntry><div class=MFile><a href="Layer/TileCache-js.html">TileCache</a></div></div><div class=MEntry><div class=MFile><a href="Layer/TMS-js.html">TMS</a></div></div><div class=MEntry><div class=MFile><a href="Layer/Vector-js.html">Vector</a></div></div><div class=MEntry><div class=MFile><a href="Layer/Vector/RootContainer-js.html">Vector.<wbr>RootContainer</a></div></div><div class=MEntry><div class=MFile><a href="Layer/WMS-js.html">WMS</a></div></div><div class=MEntry><div class=MFile><a href="Layer/WMTS-js.html">WMTS</a></div></div><div class=MEntry><div class=MFile><a href="Layer/WorldWind-js.html">WorldWind</a></div></div><div class=MEntry><div class=MFile><a href="Layer/XYZ-js.html">XYZ</a></div></div><div class=MEntry><div class=MFile><a href="Layer/Zoomify-js.html">Zoomify</a></div></div><div class=MEntry><div class=MFile><a href="Layer/OSM-js.html">OSM</a></div></div><div class=MEntry><div class=MFile><a href="Layer/UTFGrid-js.html">UTFGrid</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent24')">Marker</a><div class=MGroupContent id=MGroupContent24><div class=MEntry><div class=MFile><a href="Marker-js.html">Marker</a></div></div><div class=MEntry><div class=MFile><a href="Marker/Box-js.html">Box</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent25')">Popup</a><div class=MGroupContent id=MGroupContent25><div class=MEntry><div class=MFile><a href="Popup-js.html">Popup</a></div></div><div class=MEntry><div class=MFile><a href="Popup/Anchored-js.html">Anchored</a></div></div><div class=MEntry><div class=MFile><a href="Popup/Framed-js.html">Framed</a></div></div><div class=MEntry><div class=MFile><a href="Popup/FramedCloud-js.html">FramedCloud</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent28')">Protocol</a><div class=MGroupContent id=MGroupContent28><div class=MEntry><div class=MFile><a href="Protocol-js.html">Protocol</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent26')">Protocol</a><div class=MGroupContent id=MGroupContent26><div class=MEntry><div class=MFile><a href="Protocol/CSW-js.html">CSW</a></div></div><div class=MEntry><div class=MFile><a href="Protocol/CSW/v2_0_2-js.html">CSW.<wbr>v2_0_2</a></div></div><div class=MEntry><div class=MFile><a href="Protocol/HTTP-js.html">HTTP</a></div></div><div class=MEntry><div class=MFile><a href="Protocol/Script-js.html">Script</a></div></div><div class=MEntry><div class=MFile><a href="Protocol/SOS-js.html">SOS.<wbr>DEFAULTS</a></div></div><div class=MEntry><div class=MFile><a href="Protocol/SOS/v1_0_0-js.html">SOS.<wbr>v1_0_0</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent27')">WFS</a><div class=MGroupContent id=MGroupContent27><div class=MEntry><div class=MFile><a href="Protocol/WFS-js.html">WFS</a></div></div><div class=MEntry><div class=MFile><a href="Protocol/WFS/v1-js.html">v1</a></div></div><div class=MEntry><div class=MFile><a href="Protocol/WFS/v1_0_0-js.html">v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="Protocol/WFS/v1_1_0-js.html">v1_1_0</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent29')">Renderer</a><div class=MGroupContent id=MGroupContent29><div class=MEntry><div class=MFile><a href="Renderer-js.html">Renderer</a></div></div><div class=MEntry><div class=MFile><a href="Renderer/Canvas-js.html">Canvas</a></div></div><div class=MEntry><div class=MFile><a href="Renderer/Elements-js.html">ElementsIndexer</a></div></div><div class=MEntry><div class=MFile><a href="Renderer/SVG-js.html">SVG</a></div></div><div class=MEntry><div class=MFile><a href="Renderer/VML-js.html">VML</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent30')">Request</a><div class=MGroupContent id=MGroupContent30><div class=MEntry><div class=MFile><a href="Request-js.html">Request</a></div></div><div class=MEntry><div class=MFile><a href="Request/XMLHttpRequest-js.html">XMLHttpRequest</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent31')">Strategy</a><div class=MGroupContent id=MGroupContent31><div class=MEntry><div class=MFile><a href="Strategy-js.html">Strategy</a></div></div><div class=MEntry><div class=MFile><a href="Strategy/BBOX-js.html">BBOX</a></div></div><div class=MEntry><div class=MFile><a href="Strategy/Cluster-js.html">Cluster</a></div></div><div class=MEntry><div class=MFile><a href="Strategy/Filter-js.html">Filter</a></div></div><div class=MEntry><div class=MFile><a href="Strategy/Fixed-js.html">Fixed</a></div></div><div class=MEntry><div class=MFile><a href="Strategy/Paging-js.html">Paging</a></div></div><div class=MEntry><div class=MFile><a href="Strategy/Refresh-js.html">Refresh</a></div></div><div class=MEntry><div class=MFile><a href="Strategy/Save-js.html">Save</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent32')">Symbolizer</a><div class=MGroupContent id=MGroupContent32><div class=MEntry><div class=MFile><a href="Symbolizer-js.html">Symbolizer</a></div></div><div class=MEntry><div class=MFile><a href="Symbolizer/Line-js.html">Line</a></div></div><div class=MEntry><div class=MFile><a href="Symbolizer/Point-js.html">Point</a></div></div><div class=MEntry><div class=MFile><a href="Symbolizer/Polygon-js.html">Polygon</a></div></div><div class=MEntry><div class=MFile><a href="Symbolizer/Raster-js.html">Raster</a></div></div><div class=MEntry><div class=MFile><a href="Symbolizer/Text-js.html">Text</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent33')">Tile</a><div class=MGroupContent id=MGroupContent33><div class=MEntry><div class=MFile><a href="Tile-js.html">Tile</a></div></div><div class=MEntry><div class=MFile><a href="Tile/Image-js.html">Image</a></div></div><div class=MEntry><div class=MFile><a href="Tile/Image/IFrame-js.html">Image.<wbr>IFrame</a></div></div><div class=MEntry><div class=MFile><a href="Tile/UTFGrid-js.html">UTFGrid</a></div></div></div></div></div><div class=MEntry><div class=MFile><a href="../deprecated-js.html">Deprecated</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent34')">OpenLayers</a><div class=MGroupContent id=MGroupContent34><div class=MEntry><div class=MFile><a href="Console-js.html">Console</a></div></div><div class=MEntry><div class=MFile><a href="Events-js.html">Events</a></div></div><div class=MEntry><div class=MFile><a href="Icon-js.html">Icon</a></div></div><div class=MEntry><div class=MFile><a href="Kinetic-js.html">Kinetic</a></div></div><div class=MEntry><div class=MFile><a href="Map-js.html">Map</a></div></div><div class=MEntry><div class=MFile><a href="Projection-js.html">Projection</a></div></div><div class=MEntry><div class=MFile><a href="Rule-js.html">Rule</a></div></div><div class=MEntry><div class=MFile><a href="SingleFile-js.html">SingleFile.js</a></div></div><div class=MEntry><div class=MFile><a href="Style-js.html">Style</a></div></div><div class=MEntry><div class=MFile><a href="Style2-js.html">Style2</a></div></div><div class=MEntry><div class=MFile><a href="StyleMap-js.html">StyleMap</a></div></div><div class=MEntry><div class=MFile><a href="Tween-js.html">Tween</a></div></div><div class=MEntry><div class=MFile><a href="Util-js.html">Util</a></div></div><div class=MEntry><div class=MFile><a href="Spherical-js.html">Spherical</a></div></div><div class=MEntry><div class=MFile><a href="Animation-js.html">Animation</a></div></div><div class=MEntry><div class=MFile><a href="Events/buttonclick-js.html">Events.<wbr>buttonclick</a></div></div><div class=MEntry><div class=MFile><a href="Events/featureclick-js.html">Events.<wbr>featureclick</a></div></div><div class=MEntry><div class=MFile><a href="TileManager-js.html">TileManager</a></div></div><div class=MEntry><div class=MFile><a href="Util/vendorPrefix-js.html">Util.<wbr>vendorPrefix</a></div></div><div class=MEntry><div class=MFile><a href="WPSClient-js.html">WPSClient</a></div></div><div class=MEntry><div class=MFile><a href="WPSProcess-js.html">WPSProcess</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent36')">Index</a><div class=MGroupContent id=MGroupContent36><div class=MEntry><div class=MIndex><a href="../../index/General.html">Everything</a></div></div><div class=MEntry><div class=MIndex><a href="../../index/Classes.html">Classes</a></div></div><div class=MEntry><div class=MIndex><a href="../../index/Constants.html">Constants</a></div></div><div class=MEntry><div class=MIndex><a href="../../index/Functions.html">Functions</a></div></div><div class=MEntry><div class=MIndex><a href="../../index/Properties.html">Properties</a></div></div><div class=MEntry><div class=MIndex><a href="../../index/Files.html">Files</a></div></div><div class=MEntry><div class=MIndex><a href="../../index/Constructor.html">Constructor</a></div></div></div></div></div><script type="text/javascript"><!--
var searchPanel = new SearchPanel("searchPanel", "HTML", "../../search");
--></script><div id=MSearchPanel class=MSearchPanelInactive><input type=text id=MSearchField value=Search onFocus="searchPanel.OnSearchFieldFocus(true)" onBlur="searchPanel.OnSearchFieldFocus(false)" onKeyUp="searchPanel.OnSearchFieldChange()"><select id=MSearchType onFocus="searchPanel.OnSearchTypeFocus(true)" onBlur="searchPanel.OnSearchTypeFocus(false)" onChange="searchPanel.OnSearchTypeChange()"><option  id=MSearchEverything selected value="General">Everything</option><option value="Classes">Classes</option><option value="Constants">Constants</option><option value="Constructor">Constructor</option><option value="Files">Files</option><option value="Functions">Functions</option><option value="Properties">Properties</option></select></div><script language=JavaScript><!--
HideAllBut([18, 35], 37);// --></script></div><!--Menu-->



<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt1"><div class=CConstructor>Creates a geometry object.</div></div><div class=CToolTip id="tt2"><div class=CClass>Instances of this class represent bounding boxes. </div></div><div class=CToolTip id="tt3"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">destroy: function()</td></tr></table></blockquote>Destroy this geometry.</div></div><div class=CToolTip id="tt4"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">clone: function()</td></tr></table></blockquote>Create a clone of this geometry. </div></div><div class=CToolTip id="tt5"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>setBounds: function(</td><td class="PParameter  prettyprint " nowrap>bounds</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Set the bounds for this Geometry.</div></div><div class=CToolTip id="tt6"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">clearBounds: function()</td></tr></table></blockquote>Nullify this components bounds and that of its parent as well.</div></div><div class=CToolTip id="tt7"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>extendBounds: function(</td><td class="PParameter  prettyprint " nowrap>newBounds</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Extend the existing bounds to include the new bounds. </div></div><div class=CToolTip id="tt8"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">getBounds: function()</td></tr></table></blockquote>Get the bounds for this Geometry. </div></div><div class=CToolTip id="tt9"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">calculateBounds: function()</td></tr></table></blockquote>Recalculate the bounds for the geometry.</div></div><div class=CToolTip id="tt10"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>distanceTo: function(</td><td class="PParameter  prettyprint " nowrap>geometry,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>options</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Calculate the closest distance between two geometries (on the x-y plane).</div></div><div class=CToolTip id="tt11"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>getVertices: function(</td><td class="PParameter  prettyprint " nowrap>nodes</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Return a list of all points in this geometry.</div></div><div class=CToolTip id="tt12"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>atPoint: function(</td><td class="PParameter  prettyprint " nowrap>lonlat,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>toleranceLon,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>toleranceLat</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote></div></div><div class=CToolTip id="tt13"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">getLength: function()</td></tr></table></blockquote>Calculate the length of this geometry. </div></div><div class=CToolTip id="tt14"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">getArea: function()</td></tr></table></blockquote>Calculate the area of this geometry. </div></div><div class=CToolTip id="tt15"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">getCentroid: function()</td></tr></table></blockquote>Calculate the centroid of this geometry. </div></div><div class=CToolTip id="tt16"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">toString: function()</td></tr></table></blockquote>Returns a text representation of the geometry. </div></div><div class=CToolTip id="tt17"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>OpenLayers.Geometry.fromWKT = function(</td><td class="PParameter  prettyprint " nowrap>wkt</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Generate a geometry given a Well-Known Text string. </div></div><div class=CToolTip id="tt18"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>OpenLayers.Geometry.segmentsIntersect = function(</td><td class="PParameter  prettyprint " nowrap>seg1,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>seg2,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>options</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Determine whether two line segments intersect. </div></div><div class=CToolTip id="tt19"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>OpenLayers.Geometry.distanceToSegment = function(</td><td class="PParameter  prettyprint " nowrap>point,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>segment</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote></div></div><div class=CToolTip id="tt20"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>OpenLayers.Geometry.distanceSquaredToSegment = function(</td><td class="PParameter  prettyprint " nowrap>point,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>segment</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Usually the distanceToSegment function should be used. </div></div><div class=CToolTip id="tt21"><div class=CClass>Point geometry class.</div></div><!--END_ND_TOOLTIPS-->




<div id=MSearchResultsWindow><iframe src="" frameborder=0 name=MSearchResults id=MSearchResults></iframe><a href="javascript:searchPanel.CloseResultsWindow()" id=MSearchResultsWindowClose>Close</a></div>


<script language=JavaScript><!--
if (browserType) {if (browserVer) {document.write("</div>"); }document.write("</div>");}// --></script></body></html>