<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0//EN" "http://www.w3.org/TR/REC-html40/strict.dtd">

<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><title>OpenLayers.Bounds - OpenLayers</title><link rel="stylesheet" type="text/css" href="../../../styles/main.css"><script language=JavaScript src="../../../javascript/main.js"></script><script language=JavaScript src="../../../javascript/prettify.js"></script><script language=JavaScript src="../../../javascript/searchdata.js"></script></head><body class="ContentPage" onLoad="NDOnLoad();prettyPrint();"><script language=JavaScript><!--
if (browserType) {document.write("<div class=" + browserType + ">");if (browserVer) {document.write("<div class=" + browserVer + ">"); }}// --></script>

<!--  Generated by Natural Docs, version 1.51 -->
<!--  http://www.naturaldocs.org  -->

<!-- saved from url=(0026)http://www.naturaldocs.org -->




<div id=Content><div class="CClass"><div class=CTopic id=MainTopic><h1 class=CTitle><a name="OpenLayers.Bounds"></a>OpenLayers.<wbr>Bounds</h1><div class=CBody><p>Instances of this class represent bounding boxes.&nbsp;  Data stored as left, bottom, right, top floats.&nbsp; All values are initialized to null, however, you should make sure you set them before using the bounds for anything.</p><h4 class=CHeading>Possible use case</h4><blockquote><pre class="prettyprint">bounds = new OpenLayers.Bounds();
bounds.extend(new OpenLayers.LonLat(4,5));
bounds.extend(new OpenLayers.LonLat(5,6));
bounds.toBBOX(); // returns 4,5,5,6</pre></blockquote><!--START_ND_SUMMARY--><div class=Summary><div class=STitle>Summary</div><div class=SBorder><table border=0 cellspacing=0 cellpadding=0 class=STable><tr class="SMain"><td class=SEntry><a href="#OpenLayers.Bounds" >OpenLayers.<wbr>Bounds</a></td><td class=SDescription>Instances of this class represent bounding boxes. </td></tr><tr class="SGroup SIndent1"><td class=SEntry><a href="#OpenLayers.Bounds.Constructor" >Constructor</a></td><td class=SDescription></td></tr><tr class="SConstructor SIndent2 SMarked"><td class=SEntry><a href="#OpenLayers.Bounds.OpenLayers.Bounds" >OpenLayers.<wbr>Bounds</a></td><td class=SDescription>Construct a new bounds object. </td></tr><tr class="SGroup SIndent1"><td class=SEntry><a href="#OpenLayers.Bounds.Functions" >Functions</a></td><td class=SDescription></td></tr><tr class="SFunction SIndent2 SMarked"><td class=SEntry><a href="#OpenLayers.Bounds.toString" id=link1 onMouseOver="ShowTip(event, 'tt1', 'link1')" onMouseOut="HideTip('tt1')">toString</a></td><td class=SDescription>Returns a string representation of the bounds object.</td></tr><tr class="SFunction SIndent2"><td class=SEntry><a href="#OpenLayers.Bounds.toArray" id=link2 onMouseOver="ShowTip(event, 'tt2', 'link2')" onMouseOut="HideTip('tt2')">toArray</a></td><td class=SDescription>Returns an array representation of the bounds object.</td></tr><tr class="SFunction SIndent2 SMarked"><td class=SEntry><a href="#OpenLayers.Bounds.toBBOX" id=link3 onMouseOver="ShowTip(event, 'tt3', 'link3')" onMouseOut="HideTip('tt3')">toBBOX</a></td><td class=SDescription>Returns a boundingbox-string representation of the bounds object.</td></tr><tr class="SFunction SIndent2"><td class=SEntry><a href="#OpenLayers.Bounds.toGeometry" id=link4 onMouseOver="ShowTip(event, 'tt4', 'link4')" onMouseOut="HideTip('tt4')">toGeometry</a></td><td class=SDescription>Create a new polygon geometry based on this bounds.</td></tr><tr class="SFunction SIndent2 SMarked"><td class=SEntry><a href="#OpenLayers.Bounds.getWidth" id=link5 onMouseOver="ShowTip(event, 'tt5', 'link5')" onMouseOut="HideTip('tt5')">getWidth</a></td><td class=SDescription>Returns the width of the bounds.</td></tr><tr class="SFunction SIndent2"><td class=SEntry><a href="#OpenLayers.Bounds.getHeight" id=link6 onMouseOver="ShowTip(event, 'tt6', 'link6')" onMouseOut="HideTip('tt6')">getHeight</a></td><td class=SDescription>Returns the height of the bounds.</td></tr><tr class="SFunction SIndent2 SMarked"><td class=SEntry><a href="#OpenLayers.Bounds.getSize" id=link7 onMouseOver="ShowTip(event, 'tt7', 'link7')" onMouseOut="HideTip('tt7')">getSize</a></td><td class=SDescription>Returns an <a href="Size-js.html#OpenLayers.Size" class=LClass id=link8 onMouseOver="ShowTip(event, 'tt8', 'link8')" onMouseOut="HideTip('tt8')">OpenLayers.Size</a> object of the bounds.</td></tr><tr class="SFunction SIndent2"><td class=SEntry><a href="#OpenLayers.Bounds.getCenterPixel" id=link9 onMouseOver="ShowTip(event, 'tt9', 'link9')" onMouseOut="HideTip('tt9')">getCenterPixel</a></td><td class=SDescription>Returns the <a href="Pixel-js.html#OpenLayers.Pixel" class=LClass id=link10 onMouseOver="ShowTip(event, 'tt10', 'link10')" onMouseOut="HideTip('tt10')">OpenLayers.Pixel</a> object which represents the center of the bounds.</td></tr><tr class="SFunction SIndent2 SMarked"><td class=SEntry><a href="#OpenLayers.Bounds.getCenterLonLat" id=link11 onMouseOver="ShowTip(event, 'tt11', 'link11')" onMouseOut="HideTip('tt11')">getCenterLonLat</a></td><td class=SDescription>Returns the <a href="LonLat-js.html#OpenLayers.LonLat" class=LClass id=link12 onMouseOver="ShowTip(event, 'tt12', 'link12')" onMouseOut="HideTip('tt12')">OpenLayers.LonLat</a> object which represents the center of the bounds.</td></tr><tr class="SFunction SIndent2"><td class=SEntry><a href="#OpenLayers.Bounds.scale" id=link13 onMouseOver="ShowTip(event, 'tt13', 'link13')" onMouseOut="HideTip('tt13')">scale</a></td><td class=SDescription>Scales the bounds around a pixel or lonlat. </td></tr><tr class="SFunction SIndent2 SMarked"><td class=SEntry><a href="#OpenLayers.Bounds.add" id=link14 onMouseOver="ShowTip(event, 'tt14', 'link14')" onMouseOut="HideTip('tt14')">add</a></td><td class=SDescription>Shifts the coordinates of the bound by the given horizontal and vertical deltas.</td></tr><tr class="SFunction SIndent2"><td class=SEntry><a href="#OpenLayers.Bounds.extend" id=link15 onMouseOver="ShowTip(event, 'tt15', 'link15')" onMouseOut="HideTip('tt15')">extend</a></td><td class=SDescription>Extend the bounds to include the <a href="LonLat-js.html#OpenLayers.LonLat" class=LClass id=link16 onMouseOver="ShowTip(event, 'tt12', 'link16')" onMouseOut="HideTip('tt12')">OpenLayers.LonLat</a>, <a href="../Geometry/Point-js.html#OpenLayers.Geometry.Point" class=LClass id=link17 onMouseOver="ShowTip(event, 'tt16', 'link17')" onMouseOut="HideTip('tt16')">OpenLayers.Geometry.Point</a> or <a href="#OpenLayers.Bounds.OpenLayers.Bounds" class=LConstructor id=link18 onMouseOver="ShowTip(event, 'tt17', 'link18')" onMouseOut="HideTip('tt17')">OpenLayers.Bounds</a> specified.</td></tr><tr class="SFunction SIndent2 SMarked"><td class=SEntry><a href="#OpenLayers.Bounds.extendXY" id=link19 onMouseOver="ShowTip(event, 'tt18', 'link19')" onMouseOut="HideTip('tt18')">extendXY</a></td><td class=SDescription>Extend the bounds to include the XY coordinate specified.</td></tr><tr class="SFunction SIndent2"><td class=SEntry><a href="#OpenLayers.Bounds.containsLonLat" id=link20 onMouseOver="ShowTip(event, 'tt19', 'link20')" onMouseOut="HideTip('tt19')">containsLonLat</a></td><td class=SDescription>Returns whether the bounds object contains the given <a href="LonLat-js.html#OpenLayers.LonLat" class=LClass id=link21 onMouseOver="ShowTip(event, 'tt12', 'link21')" onMouseOut="HideTip('tt12')">OpenLayers.LonLat</a>.</td></tr><tr class="SFunction SIndent2 SMarked"><td class=SEntry><a href="#OpenLayers.Bounds.containsPixel" id=link22 onMouseOver="ShowTip(event, 'tt20', 'link22')" onMouseOut="HideTip('tt20')">containsPixel</a></td><td class=SDescription>Returns whether the bounds object contains the given <a href="Pixel-js.html#OpenLayers.Pixel" class=LClass id=link23 onMouseOver="ShowTip(event, 'tt10', 'link23')" onMouseOut="HideTip('tt10')">OpenLayers.Pixel</a>.</td></tr><tr class="SFunction SIndent2"><td class=SEntry><a href="#OpenLayers.Bounds.contains" id=link24 onMouseOver="ShowTip(event, 'tt21', 'link24')" onMouseOut="HideTip('tt21')">contains</a></td><td class=SDescription>Returns whether the bounds object contains the given x and y.</td></tr><tr class="SFunction SIndent2 SMarked"><td class=SEntry><a href="#OpenLayers.Bounds.intersectsBounds" id=link25 onMouseOver="ShowTip(event, 'tt22', 'link25')" onMouseOut="HideTip('tt22')">intersectsBounds</a></td><td class=SDescription>Determine whether the target bounds intersects this bounds. </td></tr><tr class="SFunction SIndent2"><td class=SEntry><a href="#OpenLayers.Bounds.containsBounds" id=link26 onMouseOver="ShowTip(event, 'tt23', 'link26')" onMouseOut="HideTip('tt23')">containsBounds</a></td><td class=SDescription>Returns whether the bounds object contains the given <a href="#OpenLayers.Bounds.OpenLayers.Bounds" class=LConstructor id=link27 onMouseOver="ShowTip(event, 'tt17', 'link27')" onMouseOut="HideTip('tt17')">OpenLayers.Bounds</a>.</td></tr><tr class="SFunction SIndent2 SMarked"><td class=SEntry><a href="#OpenLayers.Bounds.determineQuadrant" id=link28 onMouseOver="ShowTip(event, 'tt24', 'link28')" onMouseOut="HideTip('tt24')">determineQuadrant</a></td><td class=SDescription>Returns the the quadrant (&ldquo;br&rdquo;, &ldquo;tr&rdquo;, &ldquo;tl&rdquo;, &ldquo;bl&rdquo;) in which the given <a href="LonLat-js.html#OpenLayers.LonLat" class=LClass id=link29 onMouseOver="ShowTip(event, 'tt12', 'link29')" onMouseOut="HideTip('tt12')">OpenLayers.LonLat</a> lies.</td></tr><tr class="SFunction SIndent2"><td class=SEntry><a href="#OpenLayers.Bounds.transform" id=link30 onMouseOver="ShowTip(event, 'tt25', 'link30')" onMouseOut="HideTip('tt25')">transform</a></td><td class=SDescription>Transform the Bounds object from source to dest.</td></tr><tr class="SFunction SIndent2 SMarked"><td class=SEntry><a href="#OpenLayers.Bounds.fromString" id=link31 onMouseOver="ShowTip(event, 'tt26', 'link31')" onMouseOut="HideTip('tt26')">fromString</a></td><td class=SDescription>Alternative constructor that builds a new OpenLayers.Bounds from a parameter string.</td></tr><tr class="SFunction SIndent2"><td class=SEntry><a href="#OpenLayers.Bounds.fromArray" id=link32 onMouseOver="ShowTip(event, 'tt27', 'link32')" onMouseOut="HideTip('tt27')">fromArray</a></td><td class=SDescription>Alternative constructor that builds a new OpenLayers.Bounds from an array.</td></tr><tr class="SFunction SIndent2 SMarked"><td class=SEntry><a href="#OpenLayers.Bounds.fromSize" id=link33 onMouseOver="ShowTip(event, 'tt28', 'link33')" onMouseOut="HideTip('tt28')">fromSize</a></td><td class=SDescription>Alternative constructor that builds a new OpenLayers.Bounds from a size.</td></tr></table></div></div><!--END_ND_SUMMARY--></div></div></div>

<div class="CGroup"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Bounds.Constructor"></a>Constructor</h3></div></div>

<div class="CConstructor"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Bounds.OpenLayers.Bounds"></a>OpenLayers.<wbr>Bounds</h3><div class=CBody><p>Construct a new bounds object.&nbsp; Coordinates can either be passed as four arguments, or as a single argument.</p><h4 class=CHeading>Parameters (four arguments)</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>left</td><td class=CDLDescription>{Number} The left bounds of the box.&nbsp;  Note that for width calculations, this is assumed to be less than the right value.</td></tr><tr><td class=CDLEntry>bottom</td><td class=CDLDescription>{Number} The bottom bounds of the box.&nbsp;  Note that for height calculations, this is assumed to be less than the top value.</td></tr><tr><td class=CDLEntry>right</td><td class=CDLDescription>{Number} The right bounds.</td></tr><tr><td class=CDLEntry>top</td><td class=CDLDescription>{Number} The top bounds.</td></tr></table><h4 class=CHeading>Parameters (single argument)</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>bounds</td><td class=CDLDescription>{Array(Number)} [left, bottom, right, top]</td></tr></table></div></div></div>

<div class="CGroup"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Bounds.Functions"></a>Functions</h3></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Bounds.toString"></a>toString</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">toString:function()</td></tr></table></blockquote><p>Returns a string representation of the bounds object.</p><h4 class=CHeading>Returns</h4><p>{String} String representation of bounds object.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Bounds.toArray"></a>toArray</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>toArray: function(</td><td class="PParameter  prettyprint " nowrap>reverseAxisOrder</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote><p>Returns an array representation of the bounds object.</p><p>Returns an array of left, bottom, right, top properties, or -- when the optional parameter is true -- an array of the  bottom, left, top, right properties.</p><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>reverseAxisOrder</td><td class=CDLDescription>{Boolean} Should we reverse the axis order?</td></tr></table><h4 class=CHeading>Returns</h4><p>{Array} array of left, bottom, right, top</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Bounds.toBBOX"></a>toBBOX</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>toBBOX:function(</td><td class="PParameter  prettyprint " nowrap>decimal,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>reverseAxisOrder</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote><p>Returns a boundingbox-string representation of the bounds object.</p><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>decimal</td><td class=CDLDescription>{Integer} How many significant digits in the bbox coords?&nbsp; Default is 6</td></tr><tr><td class=CDLEntry>reverseAxisOrder</td><td class=CDLDescription>{Boolean} Should we reverse the axis order?</td></tr></table><h4 class=CHeading>Returns</h4><p>{String} Simple String representation of bounds object.&nbsp; (e.g.&nbsp; &ldquo;5,42,10,45&rdquo;)</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Bounds.toGeometry"></a>toGeometry</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">toGeometry: function()</td></tr></table></blockquote><p>Create a new polygon geometry based on this bounds.</p><h4 class=CHeading>Returns</h4><p>{<a href="../Geometry/Polygon-js.html#OpenLayers.Geometry.Polygon" class=LClass id=link34 onMouseOver="ShowTip(event, 'tt29', 'link34')" onMouseOut="HideTip('tt29')">OpenLayers.Geometry.Polygon</a>} A new polygon with the coordinates of this bounds.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Bounds.getWidth"></a>getWidth</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">getWidth:function()</td></tr></table></blockquote><p>Returns the width of the bounds.</p><h4 class=CHeading>Returns</h4><p>{Float} The width of the bounds (right minus left).</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Bounds.getHeight"></a>getHeight</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">getHeight:function()</td></tr></table></blockquote><p>Returns the height of the bounds.</p><h4 class=CHeading>Returns</h4><p>{Float} The height of the bounds (top minus bottom).</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Bounds.getSize"></a>getSize</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">getSize:function()</td></tr></table></blockquote><p>Returns an <a href="Size-js.html#OpenLayers.Size" class=LClass id=link35 onMouseOver="ShowTip(event, 'tt8', 'link35')" onMouseOut="HideTip('tt8')">OpenLayers.Size</a> object of the bounds.</p><h4 class=CHeading>Returns</h4><p>{<a href="Size-js.html#OpenLayers.Size" class=LClass id=link36 onMouseOver="ShowTip(event, 'tt8', 'link36')" onMouseOut="HideTip('tt8')">OpenLayers.Size</a>} The size of the bounds.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Bounds.getCenterPixel"></a>getCenterPixel</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">getCenterPixel:function()</td></tr></table></blockquote><p>Returns the <a href="Pixel-js.html#OpenLayers.Pixel" class=LClass id=link37 onMouseOver="ShowTip(event, 'tt10', 'link37')" onMouseOut="HideTip('tt10')">OpenLayers.Pixel</a> object which represents the center of the bounds.</p><h4 class=CHeading>Returns</h4><p>{<a href="Pixel-js.html#OpenLayers.Pixel" class=LClass id=link38 onMouseOver="ShowTip(event, 'tt10', 'link38')" onMouseOut="HideTip('tt10')">OpenLayers.Pixel</a>} The center of the bounds in pixel space.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Bounds.getCenterLonLat"></a>getCenterLonLat</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">getCenterLonLat:function()</td></tr></table></blockquote><p>Returns the <a href="LonLat-js.html#OpenLayers.LonLat" class=LClass id=link39 onMouseOver="ShowTip(event, 'tt12', 'link39')" onMouseOut="HideTip('tt12')">OpenLayers.LonLat</a> object which represents the center of the bounds.</p><h4 class=CHeading>Returns</h4><p>{<a href="LonLat-js.html#OpenLayers.LonLat" class=LClass id=link40 onMouseOver="ShowTip(event, 'tt12', 'link40')" onMouseOut="HideTip('tt12')">OpenLayers.LonLat</a>} The center of the bounds in map space.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Bounds.scale"></a>scale</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>scale: function(</td><td class="PParameter  prettyprint " nowrap>ratio,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>origin</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote><p>Scales the bounds around a pixel or lonlat.&nbsp; Note that the new bounds may return non-integer properties, even if a pixel is passed.</p><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>ratio</td><td class=CDLDescription>{Float}</td></tr><tr><td class=CDLEntry>origin</td><td class=CDLDescription>{<a href="Pixel-js.html#OpenLayers.Pixel" class=LClass id=link41 onMouseOver="ShowTip(event, 'tt10', 'link41')" onMouseOut="HideTip('tt10')">OpenLayers.Pixel</a> or <a href="LonLat-js.html#OpenLayers.LonLat" class=LClass id=link42 onMouseOver="ShowTip(event, 'tt12', 'link42')" onMouseOut="HideTip('tt12')">OpenLayers.LonLat</a>} Default is center.</td></tr></table><h4 class=CHeading>Returns</h4><p>{<a href="#OpenLayers.Bounds.OpenLayers.Bounds" class=LConstructor id=link43 onMouseOver="ShowTip(event, 'tt17', 'link43')" onMouseOut="HideTip('tt17')">OpenLayers.Bounds</a>} A new bounds that is scaled by ratio from origin.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Bounds.add"></a>add</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>add:function(</td><td class="PParameter  prettyprint " nowrap>x,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>y</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote><p>Shifts the coordinates of the bound by the given horizontal and vertical deltas.</p><blockquote><pre class="prettyprint">var bounds = new OpenLayers.Bounds(0, 0, 10, 10);
bounds.toString();
// =&gt; &quot;0,0,10,10&quot;

bounds.add(-1.5, 4).toString();
// =&gt; &quot;-1.5,4,8.5,14&quot;</pre></blockquote><p>This method will throw a TypeError if it is passed null as an argument.</p><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>x</td><td class=CDLDescription>{Float} horizontal delta</td></tr><tr><td class=CDLEntry>y</td><td class=CDLDescription>{Float} vertical delta</td></tr></table><h4 class=CHeading>Returns</h4><p>{<a href="#OpenLayers.Bounds.OpenLayers.Bounds" class=LConstructor id=link44 onMouseOver="ShowTip(event, 'tt17', 'link44')" onMouseOut="HideTip('tt17')">OpenLayers.Bounds</a>} A new bounds whose coordinates are the same as this, but shifted by the passed-in x and y values.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Bounds.extend"></a>extend</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>extend:function(</td><td class="PParameter  prettyprint " nowrap>object</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote><p>Extend the bounds to include the <a href="LonLat-js.html#OpenLayers.LonLat" class=LClass id=link45 onMouseOver="ShowTip(event, 'tt12', 'link45')" onMouseOut="HideTip('tt12')">OpenLayers.LonLat</a>, <a href="../Geometry/Point-js.html#OpenLayers.Geometry.Point" class=LClass id=link46 onMouseOver="ShowTip(event, 'tt16', 'link46')" onMouseOut="HideTip('tt16')">OpenLayers.Geometry.Point</a> or <a href="#OpenLayers.Bounds.OpenLayers.Bounds" class=LConstructor id=link47 onMouseOver="ShowTip(event, 'tt17', 'link47')" onMouseOut="HideTip('tt17')">OpenLayers.Bounds</a> specified.</p><p>Please note that this function assumes that left &lt; right and bottom &lt; top.</p><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>object</td><td class=CDLDescription>{<a href="LonLat-js.html#OpenLayers.LonLat" class=LClass id=link48 onMouseOver="ShowTip(event, 'tt12', 'link48')" onMouseOut="HideTip('tt12')">OpenLayers.LonLat</a>, <a href="../Geometry/Point-js.html#OpenLayers.Geometry.Point" class=LClass id=link49 onMouseOver="ShowTip(event, 'tt16', 'link49')" onMouseOut="HideTip('tt16')">OpenLayers.Geometry.Point</a> or <a href="#OpenLayers.Bounds.OpenLayers.Bounds" class=LConstructor id=link50 onMouseOver="ShowTip(event, 'tt17', 'link50')" onMouseOut="HideTip('tt17')">OpenLayers.Bounds</a>} The object to be included in the new bounds object.</td></tr></table></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Bounds.extendXY"></a>extendXY</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>extendXY:function(</td><td class="PParameter  prettyprint " nowrap>x,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>y</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote><p>Extend the bounds to include the XY coordinate specified.</p><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>x</td><td class=CDLDescription>{number} The X part of the the coordinate.</td></tr><tr><td class=CDLEntry>y</td><td class=CDLDescription>{number} The Y part of the the coordinate.</td></tr></table></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Bounds.containsLonLat"></a>containsLonLat</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>containsLonLat: function(</td><td class="PParameter  prettyprint " nowrap>ll,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>options</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote><p>Returns whether the bounds object contains the given <a href="LonLat-js.html#OpenLayers.LonLat" class=LClass id=link51 onMouseOver="ShowTip(event, 'tt12', 'link51')" onMouseOut="HideTip('tt12')">OpenLayers.LonLat</a>.</p><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>ll</td><td class=CDLDescription>{&lt;OpenLayers.LonLat&gt;|Object} OpenLayers.LonLat or an object with a &lsquo;lon&rsquo; and &lsquo;lat&rsquo; properties.</td></tr><tr><td class=CDLEntry>options</td><td class=CDLDescription>{Object} Optional parameters</td></tr></table><h4 class=CHeading>Acceptable options</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>inclusive</td><td class=CDLDescription>{Boolean} Whether or not to include the border.&nbsp; Default is true.</td></tr><tr><td class=CDLEntry>worldBounds</td><td class=CDLDescription>{<a href="#OpenLayers.Bounds.OpenLayers.Bounds" class=LConstructor id=link52 onMouseOver="ShowTip(event, 'tt17', 'link52')" onMouseOut="HideTip('tt17')">OpenLayers.Bounds</a>} If a worldBounds is provided, the ll will be considered as contained if it exceeds the world bounds, but can be wrapped around the dateline so it is contained by this bounds.</td></tr></table><h4 class=CHeading>Returns</h4><p>{Boolean} The passed-in lonlat is within this bounds.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Bounds.containsPixel"></a>containsPixel</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>containsPixel:function(</td><td class="PParameter  prettyprint " nowrap>px,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>inclusive</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote><p>Returns whether the bounds object contains the given <a href="Pixel-js.html#OpenLayers.Pixel" class=LClass id=link53 onMouseOver="ShowTip(event, 'tt10', 'link53')" onMouseOut="HideTip('tt10')">OpenLayers.Pixel</a>.</p><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>px</td><td class=CDLDescription>{<a href="Pixel-js.html#OpenLayers.Pixel" class=LClass id=link54 onMouseOver="ShowTip(event, 'tt10', 'link54')" onMouseOut="HideTip('tt10')">OpenLayers.Pixel</a>}</td></tr><tr><td class=CDLEntry>inclusive</td><td class=CDLDescription>{Boolean} Whether or not to include the border.&nbsp; Default is true.</td></tr></table><h4 class=CHeading>Returns</h4><p>{Boolean} The passed-in pixel is within this bounds.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Bounds.contains"></a>contains</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>contains:function(</td><td class="PParameter  prettyprint " nowrap>x,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>y,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>inclusive</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote><p>Returns whether the bounds object contains the given x and y.</p><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>x</td><td class=CDLDescription>{Float}</td></tr><tr><td class=CDLEntry>y</td><td class=CDLDescription>{Float}</td></tr><tr><td class=CDLEntry>inclusive</td><td class=CDLDescription>{Boolean} Whether or not to include the border.&nbsp; Default is true.</td></tr></table><h4 class=CHeading>Returns</h4><p>{Boolean} Whether or not the passed-in coordinates are within this bounds.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Bounds.intersectsBounds"></a>intersectsBounds</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>intersectsBounds:function(</td><td class="PParameter  prettyprint " nowrap>bounds,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>options</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote><p>Determine whether the target bounds intersects this bounds.&nbsp;  Bounds are considered intersecting if any of their edges intersect or if one bounds contains the other.</p><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>bounds</td><td class=CDLDescription>{<a href="#OpenLayers.Bounds.OpenLayers.Bounds" class=LConstructor id=link55 onMouseOver="ShowTip(event, 'tt17', 'link55')" onMouseOut="HideTip('tt17')">OpenLayers.Bounds</a>} The target bounds.</td></tr><tr><td class=CDLEntry>options</td><td class=CDLDescription>{Object} Optional parameters.</td></tr></table><h4 class=CHeading>Acceptable options</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>inclusive</td><td class=CDLDescription>{Boolean} Treat coincident borders as intersecting.&nbsp;  Default is true.&nbsp;  If false, bounds that do not overlap but only touch at the border will not be considered as intersecting.</td></tr><tr><td class=CDLEntry>worldBounds</td><td class=CDLDescription>{<a href="#OpenLayers.Bounds.OpenLayers.Bounds" class=LConstructor id=link56 onMouseOver="ShowTip(event, 'tt17', 'link56')" onMouseOut="HideTip('tt17')">OpenLayers.Bounds</a>} If a worldBounds is provided, two bounds will be considered as intersecting if they intersect when shifted to within the world bounds.&nbsp;  This applies only to bounds that cross or are completely outside the world bounds.</td></tr></table><h4 class=CHeading>Returns</h4><p>{Boolean} The passed-in bounds object intersects this bounds.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Bounds.containsBounds"></a>containsBounds</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>containsBounds:function(</td><td class="PParameter  prettyprint " nowrap>bounds,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>partial,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>inclusive</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote><p>Returns whether the bounds object contains the given <a href="#OpenLayers.Bounds.OpenLayers.Bounds" class=LConstructor id=link57 onMouseOver="ShowTip(event, 'tt17', 'link57')" onMouseOut="HideTip('tt17')">OpenLayers.Bounds</a>.</p><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>bounds</td><td class=CDLDescription>{<a href="#OpenLayers.Bounds.OpenLayers.Bounds" class=LConstructor id=link58 onMouseOver="ShowTip(event, 'tt17', 'link58')" onMouseOut="HideTip('tt17')">OpenLayers.Bounds</a>} The target bounds.</td></tr><tr><td class=CDLEntry>partial</td><td class=CDLDescription>{Boolean} If any of the target corners is within this bounds consider the bounds contained.&nbsp;  Default is false.&nbsp;  If false, the entire target bounds must be contained within this bounds.</td></tr><tr><td class=CDLEntry>inclusive</td><td class=CDLDescription>{Boolean} Treat shared edges as contained.&nbsp;  Default is true.</td></tr></table><h4 class=CHeading>Returns</h4><p>{Boolean} The passed-in bounds object is contained within this bounds.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Bounds.determineQuadrant"></a>determineQuadrant</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>determineQuadrant: function(</td><td class="PParameter  prettyprint " nowrap>lonlat</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote><p>Returns the the quadrant (&ldquo;br&rdquo;, &ldquo;tr&rdquo;, &ldquo;tl&rdquo;, &ldquo;bl&rdquo;) in which the given <a href="LonLat-js.html#OpenLayers.LonLat" class=LClass id=link59 onMouseOver="ShowTip(event, 'tt12', 'link59')" onMouseOut="HideTip('tt12')">OpenLayers.LonLat</a> lies.</p><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>lonlat</td><td class=CDLDescription>{<a href="LonLat-js.html#OpenLayers.LonLat" class=LClass id=link60 onMouseOver="ShowTip(event, 'tt12', 'link60')" onMouseOut="HideTip('tt12')">OpenLayers.LonLat</a>}</td></tr></table><h4 class=CHeading>Returns</h4><p>{String} The quadrant (&ldquo;br&rdquo; &ldquo;tr&rdquo; &ldquo;tl&rdquo; &ldquo;bl&rdquo;) of the bounds in which the coordinate lies.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Bounds.transform"></a>transform</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>transform: function(</td><td class="PParameter  prettyprint " nowrap>source,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>dest</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote><p>Transform the Bounds object from source to dest.</p><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>source</td><td class=CDLDescription>{<a href="../Projection-js.html#OpenLayers.Projection" class=LClass id=link61 onMouseOver="ShowTip(event, 'tt30', 'link61')" onMouseOut="HideTip('tt30')">OpenLayers.Projection</a>} Source projection.</td></tr><tr><td class=CDLEntry>dest</td><td class=CDLDescription>{<a href="../Projection-js.html#OpenLayers.Projection" class=LClass id=link62 onMouseOver="ShowTip(event, 'tt30', 'link62')" onMouseOut="HideTip('tt30')">OpenLayers.Projection</a>} Destination projection.</td></tr></table><h4 class=CHeading>Returns</h4><p>{<a href="#OpenLayers.Bounds.OpenLayers.Bounds" class=LConstructor id=link63 onMouseOver="ShowTip(event, 'tt17', 'link63')" onMouseOut="HideTip('tt17')">OpenLayers.Bounds</a>} Itself, for use in chaining operations.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Bounds.fromString"></a>fromString</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>OpenLayers.Bounds.fromString = function(</td><td class="PParameter  prettyprint " nowrap>str,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>reverseAxisOrder</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote><p>Alternative constructor that builds a new OpenLayers.Bounds from a parameter string.</p><blockquote><pre class="prettyprint">OpenLayers.Bounds.fromString(&quot;5,42,10,45&quot;);
// =&gt; equivalent to ...
new OpenLayers.Bounds(5, 42, 10, 45);</pre></blockquote><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>str</td><td class=CDLDescription>{String} Comma-separated bounds string.&nbsp; (e.g.&nbsp; &ldquo;5,42,10,45&rdquo;)</td></tr><tr><td class=CDLEntry>reverseAxisOrder</td><td class=CDLDescription>{Boolean} Does the string use reverse axis order?</td></tr></table><h4 class=CHeading>Returns</h4><p>{<a href="#OpenLayers.Bounds.OpenLayers.Bounds" class=LConstructor id=link64 onMouseOver="ShowTip(event, 'tt17', 'link64')" onMouseOut="HideTip('tt17')">OpenLayers.Bounds</a>} New bounds object built from the passed-in String.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Bounds.fromArray"></a>fromArray</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>OpenLayers.Bounds.fromArray = function(</td><td class="PParameter  prettyprint " nowrap>bbox,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>reverseAxisOrder</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote><p>Alternative constructor that builds a new OpenLayers.Bounds from an array.</p><blockquote><pre class="prettyprint">OpenLayers.Bounds.fromArray( [5, 42, 10, 45] );
// =&gt; equivalent to ...
new OpenLayers.Bounds(5, 42, 10, 45);</pre></blockquote><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>bbox</td><td class=CDLDescription>{Array(Float)} Array of bounds values (e.g.&nbsp; [5,42,10,45])</td></tr><tr><td class=CDLEntry>reverseAxisOrder</td><td class=CDLDescription>{Boolean} Does the array use reverse axis order?</td></tr></table><h4 class=CHeading>Returns</h4><p>{<a href="#OpenLayers.Bounds.OpenLayers.Bounds" class=LConstructor id=link65 onMouseOver="ShowTip(event, 'tt17', 'link65')" onMouseOut="HideTip('tt17')">OpenLayers.Bounds</a>} New bounds object built from the passed-in Array.</p></div></div></div>

<div class="CFunction"><div class=CTopic><h3 class=CTitle><a name="OpenLayers.Bounds.fromSize"></a>fromSize</h3><div class=CBody><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>OpenLayers.Bounds.fromSize = function(</td><td class="PParameter  prettyprint " nowrap>size</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote><p>Alternative constructor that builds a new OpenLayers.Bounds from a size.</p><blockquote><pre class="prettyprint">OpenLayers.Bounds.fromSize( new OpenLayers.Size(10, 20) );
// =&gt; equivalent to ...
new OpenLayers.Bounds(0, 20, 10, 0);</pre></blockquote><h4 class=CHeading>Parameters</h4><table border=0 cellspacing=0 cellpadding=0 class=CDescriptionList><tr><td class=CDLEntry>size</td><td class=CDLDescription>{<a href="Size-js.html#OpenLayers.Size" class=LClass id=link66 onMouseOver="ShowTip(event, 'tt8', 'link66')" onMouseOut="HideTip('tt8')">OpenLayers.Size</a> or Object} <a href="Size-js.html#OpenLayers.Size" class=LClass id=link67 onMouseOver="ShowTip(event, 'tt8', 'link67')" onMouseOut="HideTip('tt8')">OpenLayers.Size</a> or an object with both &lsquo;w&rsquo; and &lsquo;h&rsquo; properties.</td></tr></table><h4 class=CHeading>Returns</h4><p>{<a href="#OpenLayers.Bounds.OpenLayers.Bounds" class=LConstructor id=link68 onMouseOver="ShowTip(event, 'tt17', 'link68')" onMouseOut="HideTip('tt17')">OpenLayers.Bounds</a>} New bounds object built from the passed-in size.</p></div></div></div>

</div><!--Content-->


<div id=Footer><a href="http://www.naturaldocs.org">Generated by Natural Docs</a></div><!--Footer-->


<div id=Menu><div class=MTitle>OpenLayers<div class=MSubTitle>JavaScript Mapping Library</div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent35')">OpenLayers</a><div class=MGroupContent id=MGroupContent35><div class=MEntry><div class=MFile><a href="../../OpenLayers-js.html">OpenLayers</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent1')">BaseTypes</a><div class=MGroupContent id=MGroupContent1><div class=MEntry><div class=MFile><a href="../BaseTypes-js.html">Base Types</a></div></div><div class=MEntry><div class=MFile id=MSelected>Bounds</div></div><div class=MEntry><div class=MFile><a href="Class-js.html">Class</a></div></div><div class=MEntry><div class=MFile><a href="Date-js.html">Date</a></div></div><div class=MEntry><div class=MFile><a href="Element-js.html">Element</a></div></div><div class=MEntry><div class=MFile><a href="LonLat-js.html">LonLat</a></div></div><div class=MEntry><div class=MFile><a href="Pixel-js.html">Pixel</a></div></div><div class=MEntry><div class=MFile><a href="Size-js.html">Size</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent3')">Control</a><div class=MGroupContent id=MGroupContent3><div class=MEntry><div class=MFile><a href="../Control-js.html">Control</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent2')">Control</a><div class=MGroupContent id=MGroupContent2><div class=MEntry><div class=MFile><a href="../Control/ArgParser-js.html">ArgParser</a></div></div><div class=MEntry><div class=MFile><a href="../Control/Attribution-js.html">Attribution</a></div></div><div class=MEntry><div class=MFile><a href="../Control/Button-js.html">Button</a></div></div><div class=MEntry><div class=MFile><a href="../Control/CacheRead-js.html">CacheRead</a></div></div><div class=MEntry><div class=MFile><a href="../Control/CacheWrite-js.html">CacheWrite</a></div></div><div class=MEntry><div class=MFile><a href="../Control/DragFeature-js.html">DragFeature</a></div></div><div class=MEntry><div class=MFile><a href="../Control/DragPan-js.html">DragPan</a></div></div><div class=MEntry><div class=MFile><a href="../Control/DrawFeature-js.html">DrawFeature</a></div></div><div class=MEntry><div class=MFile><a href="../Control/EditingToolbar-js.html">EditingToolbar</a></div></div><div class=MEntry><div class=MFile><a href="../Control/Geolocate-js.html">Geolocate</a></div></div><div class=MEntry><div class=MFile><a href="../Control/GetFeature-js.html">GetFeature</a></div></div><div class=MEntry><div class=MFile><a href="../Control/Graticule-js.html">Graticule</a></div></div><div class=MEntry><div class=MFile><a href="../Control/KeyboardDefaults-js.html">KeyboardDefaults</a></div></div><div class=MEntry><div class=MFile><a href="../Control/LayerSwitcher-js.html">LayerSwitcher</a></div></div><div class=MEntry><div class=MFile><a href="../Control/Measure-js.html">Measure</a></div></div><div class=MEntry><div class=MFile><a href="../Control/ModifyFeature-js.html">ModifyFeature</a></div></div><div class=MEntry><div class=MFile><a href="../Control/MousePosition-js.html">MousePosition</a></div></div><div class=MEntry><div class=MFile><a href="../Control/Navigation-js.html">Navigation</a></div></div><div class=MEntry><div class=MFile><a href="../Control/NavigationHistory-js.html">NavigationHistory</a></div></div><div class=MEntry><div class=MFile><a href="../Control/NavToolbar-js.html">NavToolbar</a></div></div><div class=MEntry><div class=MFile><a href="../Control/OverviewMap-js.html">OverviewMap</a></div></div><div class=MEntry><div class=MFile><a href="../Control/Pan-js.html">Pan</a></div></div><div class=MEntry><div class=MFile><a href="../Control/Panel-js.html">Panel</a></div></div><div class=MEntry><div class=MFile><a href="../Control/PanPanel-js.html">PanPanel</a></div></div><div class=MEntry><div class=MFile><a href="../Control/PanZoom-js.html">PanZoom</a></div></div><div class=MEntry><div class=MFile><a href="../Control/PanZoomBar-js.html">PanZoomBar</a></div></div><div class=MEntry><div class=MFile><a href="../Control/Permalink-js.html">Permalink</a></div></div><div class=MEntry><div class=MFile><a href="../Control/PinchZoom-js.html">PinchZoom</a></div></div><div class=MEntry><div class=MFile><a href="../Control/Scale-js.html">Scale</a></div></div><div class=MEntry><div class=MFile><a href="../Control/ScaleLine-js.html">ScaleLine</a></div></div><div class=MEntry><div class=MFile><a href="../Control/SelectFeature-js.html">SelectFeature</a></div></div><div class=MEntry><div class=MFile><a href="../Control/SLDSelect-js.html">SLDSelect</a></div></div><div class=MEntry><div class=MFile><a href="../Control/Snapping-js.html">Snapping</a></div></div><div class=MEntry><div class=MFile><a href="../Control/Split-js.html">Split</a></div></div><div class=MEntry><div class=MFile><a href="../Control/TouchNavigation-js.html">TouchNavigation</a></div></div><div class=MEntry><div class=MFile><a href="../Control/TransformFeature-js.html">TransformFeature</a></div></div><div class=MEntry><div class=MFile><a href="../Control/UTFGrid-js.html">UTFGrid</a></div></div><div class=MEntry><div class=MFile><a href="../Control/WMSGetFeatureInfo-js.html">WMSGetFeatureInfo</a></div></div><div class=MEntry><div class=MFile><a href="../Control/WMTSGetFeatureInfo-js.html">WMTSGetFeatureInfo</a></div></div><div class=MEntry><div class=MFile><a href="../Control/Zoom-js.html">Zoom</a></div></div><div class=MEntry><div class=MFile><a href="../Control/ZoomBox-js.html">ZoomBox</a></div></div><div class=MEntry><div class=MFile><a href="../Control/ZoomIn-js.html">ZoomIn</a></div></div><div class=MEntry><div class=MFile><a href="../Control/ZoomOut-js.html">ZoomOut</a></div></div><div class=MEntry><div class=MFile><a href="../Control/ZoomPanel-js.html">ZoomPanel</a></div></div><div class=MEntry><div class=MFile><a href="../Control/ZoomToMaxExtent-js.html">ZoomToMaxExtent</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent4')">Feature</a><div class=MGroupContent id=MGroupContent4><div class=MEntry><div class=MFile><a href="../Feature-js.html">Feature</a></div></div><div class=MEntry><div class=MFile><a href="../Feature/Vector-js.html">Vector</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent5')">Filter</a><div class=MGroupContent id=MGroupContent5><div class=MEntry><div class=MFile><a href="../Filter-js.html">Filter</a></div></div><div class=MEntry><div class=MFile><a href="../Filter/Comparison-js.html">Comparison</a></div></div><div class=MEntry><div class=MFile><a href="../Filter/FeatureId-js.html">FeatureId</a></div></div><div class=MEntry><div class=MFile><a href="../Filter/Function-js.html">Function</a></div></div><div class=MEntry><div class=MFile><a href="../Filter/Logical-js.html">Logical</a></div></div><div class=MEntry><div class=MFile><a href="../Filter/Spatial-js.html">Spatial</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent17')">Format</a><div class=MGroupContent id=MGroupContent17><div class=MEntry><div class=MFile><a href="../Format-js.html">Format</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent6')">Filter</a><div class=MGroupContent id=MGroupContent6><div class=MEntry><div class=MFile><a href="../Format/Filter-js.html">Filter</a></div></div><div class=MEntry><div class=MFile><a href="../Format/Filter/v1-js.html">v1</a></div></div><div class=MEntry><div class=MFile><a href="../Format/Filter/v1_0_0-js.html">v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="../Format/Filter/v1_1_0-js.html">v1_1_0</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent7')">GML</a><div class=MGroupContent id=MGroupContent7><div class=MEntry><div class=MFile><a href="../Format/GML-js.html">GML</a></div></div><div class=MEntry><div class=MFile><a href="../Format/GML/Base-js.html">Base</a></div></div><div class=MEntry><div class=MFile><a href="../Format/GML/v2-js.html">v2</a></div></div><div class=MEntry><div class=MFile><a href="../Format/GML/v3-js.html">v3</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent8')">SLD</a><div class=MGroupContent id=MGroupContent8><div class=MEntry><div class=MFile><a href="../Format/SLD-js.html">SLD</a></div></div><div class=MEntry><div class=MFile><a href="../Format/SLD/v1_0_0_GeoServer-js.html">SLD/<wbr>v1_0_0_GeoServer</a></div></div><div class=MEntry><div class=MFile><a href="../Format/SLD/v1-js.html">v1</a></div></div><div class=MEntry><div class=MFile><a href="../Format/SLD/v1_0_0-js.html">v1_0_0</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent9')">OWSCommon</a><div class=MGroupContent id=MGroupContent9><div class=MEntry><div class=MFile><a href="../Format/OWSCommon-js.html">OWSCommon</a></div></div><div class=MEntry><div class=MFile><a href="../Format/OWSCommon/v1-js.html">v1</a></div></div><div class=MEntry><div class=MFile><a href="../Format/OWSCommon/v1_0_0-js.html">v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="../Format/OWSCommon/v1_1_0-js.html">v1_1_0</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent10')">WFSCapabilities</a><div class=MGroupContent id=MGroupContent10><div class=MEntry><div class=MFile><a href="../Format/WFSCapabilities-js.html">WFSCapabilities</a></div></div><div class=MEntry><div class=MFile><a href="../Format/WFSCapabilities/v1-js.html">v1</a></div></div><div class=MEntry><div class=MFile><a href="../Format/WFSCapabilities/v1_0_0-js.html">v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="../Format/WFSCapabilities/v1_1_0-js.html">v1_1_0</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent11')">WFST</a><div class=MGroupContent id=MGroupContent11><div class=MEntry><div class=MFile><a href="../Format/WFST-js.html">WFST</a></div></div><div class=MEntry><div class=MFile><a href="../Format/WFST/v1-js.html">v1</a></div></div><div class=MEntry><div class=MFile><a href="../Format/WFST/v1_0_0-js.html">v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="../Format/WFST/v1_1_0-js.html">v1_1_0</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent12')">WMC</a><div class=MGroupContent id=MGroupContent12><div class=MEntry><div class=MFile><a href="../Format/WMC-js.html">WMC</a></div></div><div class=MEntry><div class=MFile><a href="../Format/WMC/v1-js.html">v1</a></div></div><div class=MEntry><div class=MFile><a href="../Format/WMC/v1_0_0-js.html">v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="../Format/WMC/v1_1_0-js.html">v1_1_0</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent13')">WMSCapabilities</a><div class=MGroupContent id=MGroupContent13><div class=MEntry><div class=MFile><a href="../Format/WMSCapabilities-js.html">WMSCapabilities</a></div></div><div class=MEntry><div class=MFile><a href="../Format/WMSCapabilities/v1-js.html">v1</a></div></div><div class=MEntry><div class=MFile><a href="../Format/WMSCapabilities/v1_1-js.html">v1_1</a></div></div><div class=MEntry><div class=MFile><a href="../Format/WMSCapabilities/v1_1_0-js.html">v1_1_0</a></div></div><div class=MEntry><div class=MFile><a href="../Format/WMSCapabilities/v1_1_1-js.html">v1_1_1</a></div></div><div class=MEntry><div class=MFile><a href="../Format/WMSCapabilities/v1_3-js.html">v1_3</a></div></div><div class=MEntry><div class=MFile><a href="../Format/WMSCapabilities/v1_3_0-js.html">v1_3_0</a></div></div><div class=MEntry><div class=MFile><a href="../Format/WMSCapabilities/v1_1_1_WMSC-js.html">WMSCapabilities/<wbr>v1_1_1_WMSC</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent14')">WMSDescribeLayer</a><div class=MGroupContent id=MGroupContent14><div class=MEntry><div class=MFile><a href="../Format/WMSDescribeLayer-js.html">WMSDescribeLayer</a></div></div><div class=MEntry><div class=MFile><a href="../Format/WMSDescribeLayer/v1_1-js.html">v1_1</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent16')">Format</a><div class=MGroupContent id=MGroupContent16><div class=MEntry><div class=MFile><a href="../Format/ArcXML-js.html">ArcXML</a></div></div><div class=MEntry><div class=MFile><a href="../Format/ArcXML/Features-js.html">ArcXML.<wbr>Features</a></div></div><div class=MEntry><div class=MFile><a href="../Format/Atom-js.html">Atom</a></div></div><div class=MEntry><div class=MFile><a href="../Format/Context-js.html">Context</a></div></div><div class=MEntry><div class=MFile><a href="../Format/CQL-js.html">CQL</a></div></div><div class=MEntry><div class=MFile><a href="../Format/CSWGetDomain-js.html">CSWGetDomain</a></div></div><div class=MEntry><div class=MFile><a href="../Format/CSWGetDomain/v2_0_2-js.html">CSWGetDomain.<wbr>v2_0_2</a></div></div><div class=MEntry><div class=MFile><a href="../Format/CSWGetRecords-js.html">CSWGetRecords</a></div></div><div class=MEntry><div class=MFile><a href="../Format/CSWGetRecords/v2_0_2-js.html">CSWGetRecords.<wbr>v2_0_2</a></div></div><div class=MEntry><div class=MFile><a href="../Format/EncodedPolyline-js.html">EncodedPolyline</a></div></div><div class=MEntry><div class=MFile><a href="../Format/GeoJSON-js.html">GeoJSON</a></div></div><div class=MEntry><div class=MFile><a href="../Format/GeoRSS-js.html">GeoRSS</a></div></div><div class=MEntry><div class=MFile><a href="../Format/GPX-js.html">GPX</a></div></div><div class=MEntry><div class=MFile><a href="../Format/JSON-js.html">JSON</a></div></div><div class=MEntry><div class=MFile><a href="../Format/KML-js.html">KML</a></div></div><div class=MEntry><div class=MFile><a href="../Format/OGCExceptionReport-js.html">OGCExceptionReport</a></div></div><div class=MEntry><div class=MFile><a href="../Format/OSM-js.html">OSM</a></div></div><div class=MEntry><div class=MFile><a href="../Format/OWSContext-js.html">OWSContext</a></div></div><div class=MEntry><div class=MFile><a href="../Format/OWSContext/v0_3_1-js.html">OWSContext.<wbr>v0_3_1</a></div></div><div class=MEntry><div class=MFile><a href="../Format/QueryStringFilter-js.html">QueryStringFilter</a></div></div><div class=MEntry><div class=MFile><a href="../Format/SOSCapabilities-js.html">SOSCapabilities</a></div></div><div class=MEntry><div class=MFile><a href="../Format/SOSCapabilities/v1_0_0-js.html">SOSCapabilities.<wbr>v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="../Format/SOSGetFeatureOfInterest-js.html">SOSGetFeatureOfInterest</a></div></div><div class=MEntry><div class=MFile><a href="../Format/SOSGetObservation-js.html">SOSGetObservation</a></div></div><div class=MEntry><div class=MFile><a href="../Format/Text-js.html">Text</a></div></div><div class=MEntry><div class=MFile><a href="../Format/WCSCapabilities-js.html">WCSCapabilities</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent15')">WCSCapabilities</a><div class=MGroupContent id=MGroupContent15><div class=MEntry><div class=MFile><a href="../Format/WCSCapabilities/v1-js.html">WCSCapabilities.v1</a></div></div><div class=MEntry><div class=MFile><a href="../Format/WCSCapabilities/v1_0_0-js.html">WCSCapabilities/<wbr>v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="../Format/WCSCapabilities/v1_1_0-js.html">WCSCapabilities/<wbr>v1_1_0</a></div></div></div></div></div><div class=MEntry><div class=MFile><a href="../Format/WCSGetCoverage-js.html">WCSGetCoverage version 1.1.0</a></div></div><div class=MEntry><div class=MFile><a href="../Format/WFS-js.html">WFS</a></div></div><div class=MEntry><div class=MFile><a href="../Format/WFSDescribeFeatureType-js.html">WFSDescribeFeatureType</a></div></div><div class=MEntry><div class=MFile><a href="../Format/WKT-js.html">WKT</a></div></div><div class=MEntry><div class=MFile><a href="../Format/WMSGetFeatureInfo-js.html">WMSGetFeatureInfo</a></div></div><div class=MEntry><div class=MFile><a href="../Format/WMTSCapabilities-js.html">WMTSCapabilities</a></div></div><div class=MEntry><div class=MFile><a href="../Format/WMTSCapabilities/v1_0_0-js.html">WMTSCapabilities.<wbr>v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="../Format/WPSCapabilities-js.html">WPSCapabilities</a></div></div><div class=MEntry><div class=MFile><a href="../Format/WPSCapabilities/v1_0_0-js.html">WPSCapabilities.<wbr>v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="../Format/WPSDescribeProcess-js.html">WPSDescribeProcess</a></div></div><div class=MEntry><div class=MFile><a href="../Format/WPSExecute-js.html">WPSExecute version 1.0.0</a></div></div><div class=MEntry><div class=MFile><a href="../Format/XLS-js.html">XLS</a></div></div><div class=MEntry><div class=MFile><a href="../Format/XLS/v1-js.html">XLS.v1</a></div></div><div class=MEntry><div class=MFile><a href="../Format/XLS/v1_1_0-js.html">XLS.<wbr>v1_1_0</a></div></div><div class=MEntry><div class=MFile><a href="../Format/XML-js.html">XML</a></div></div><div class=MEntry><div class=MFile><a href="../Format/XML/VersionedOGC-js.html">XML.<wbr>VersionedOGC</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent18')">Geometry</a><div class=MGroupContent id=MGroupContent18><div class=MEntry><div class=MFile><a href="../Geometry-js.html">Geometry</a></div></div><div class=MEntry><div class=MFile><a href="../Geometry/Collection-js.html">Collection</a></div></div><div class=MEntry><div class=MFile><a href="../Geometry/Curve-js.html">Curve</a></div></div><div class=MEntry><div class=MFile><a href="../Geometry/LinearRing-js.html">LinearRing</a></div></div><div class=MEntry><div class=MFile><a href="../Geometry/LineString-js.html">LineString</a></div></div><div class=MEntry><div class=MFile><a href="../Geometry/MultiLineString-js.html">MultiLineString</a></div></div><div class=MEntry><div class=MFile><a href="../Geometry/MultiPoint-js.html">MultiPoint</a></div></div><div class=MEntry><div class=MFile><a href="../Geometry/MultiPolygon-js.html">MultiPolygon</a></div></div><div class=MEntry><div class=MFile><a href="../Geometry/Point-js.html">Point</a></div></div><div class=MEntry><div class=MFile><a href="../Geometry/Polygon-js.html">Polygon</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent19')">Handler</a><div class=MGroupContent id=MGroupContent19><div class=MEntry><div class=MFile><a href="../Handler-js.html">Handler</a></div></div><div class=MEntry><div class=MFile><a href="../Handler/Box-js.html">Box</a></div></div><div class=MEntry><div class=MFile><a href="../Handler/Click-js.html">Click</a></div></div><div class=MEntry><div class=MFile><a href="../Handler/Drag-js.html">Drag</a></div></div><div class=MEntry><div class=MFile><a href="../Handler/Feature-js.html">Feature</a></div></div><div class=MEntry><div class=MFile><a href="../Handler/Hover-js.html">Hover</a></div></div><div class=MEntry><div class=MFile><a href="../Handler/Keyboard-js.html">Keyboard</a></div></div><div class=MEntry><div class=MFile><a href="../Handler/MouseWheel-js.html">MouseWheel</a></div></div><div class=MEntry><div class=MFile><a href="../Handler/Path-js.html">Path</a></div></div><div class=MEntry><div class=MFile><a href="../Handler/Pinch-js.html">Pinch</a></div></div><div class=MEntry><div class=MFile><a href="../Handler/Point-js.html">Point</a></div></div><div class=MEntry><div class=MFile><a href="../Handler/Polygon-js.html">Polygon</a></div></div><div class=MEntry><div class=MFile><a href="../Handler/RegularPolygon-js.html">RegularPolygon</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent21')">Lang</a><div class=MGroupContent id=MGroupContent21><div class=MEntry><div class=MFile><a href="../Lang-js.html">Lang</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent20')">Lang</a><div class=MGroupContent id=MGroupContent20><div class=MEntry><div class=MFile><a href="../Lang/ar-js.html">ar</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/be-tarask-js.html">be-tarask</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/bg-js.html">bg</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/br-js.html">br</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/ca-js.html">ca</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/cs-CZ-js.html">cs-CZ</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/da-DK-js.html">da-DK</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/de-js.html">de</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/en-js.html">en</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/en-CA-js.html">en-CA</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/es-js.html">es</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/el-js.html">el</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/fi-js.html">fi</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/fr-js.html">fr</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/fur-js.html">fur</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/gl-js.html">gl</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/gsw-js.html">gsw</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/hr-js.html">hr</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/hsb-js.html">hsb</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/hu-js.html">hu</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/ia-js.html">ia</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/id-js.html">id</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/io-js.html">io</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/is-js.html">is</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/it-js.html">it</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/ja-js.html">ja</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/km-js.html">km</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/ksh-js.html">ksh</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/lt-js.html">lt</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/nds-js.html">nds</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/nb-js.html">nb</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/nl-js.html">nl</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/nn-js.html">nn</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/oc-js.html">oc</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/pt-js.html">pt</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/pl-js.html">pl</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/pt-BR-js.html">pt-BR</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/ru-js.html">ru</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/sk-js.html">sk</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/sv-SE-js.html">sv-SE</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/te-js.html">te</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/vi-js.html">vi</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/zh-CN-js.html">zh-CN</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/zh-TW-js.html">zh-TW</a></div></div><div class=MEntry><div class=MFile><a href="../Lang/ro-js.html">Lang[&ldquo;ro&rdquo;]</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent23')">Layer</a><div class=MGroupContent id=MGroupContent23><div class=MEntry><div class=MFile><a href="../Layer-js.html">Layer</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent22')">Layer</a><div class=MGroupContent id=MGroupContent22><div class=MEntry><div class=MFile><a href="../Layer/ArcGISCache-js.html">ArcGISCache.js</a></div></div><div class=MEntry><div class=MFile><a href="../Layer/ArcGIS93Rest-js.html">ArcGIS93Rest</a></div></div><div class=MEntry><div class=MFile><a href="../Layer/ArcIMS-js.html">ArcIMS</a></div></div><div class=MEntry><div class=MFile><a href="../Layer/Bing-js.html">Bing</a></div></div><div class=MEntry><div class=MFile><a href="../Layer/Boxes-js.html">Boxes</a></div></div><div class=MEntry><div class=MFile><a href="../Layer/EventPane-js.html">EventPane</a></div></div><div class=MEntry><div class=MFile><a href="../Layer/FixedZoomLevels-js.html">FixedZoomLevels</a></div></div><div class=MEntry><div class=MFile><a href="../Layer/GeoRSS-js.html">GeoRSS</a></div></div><div class=MEntry><div class=MFile><a href="../Layer/Google-js.html">Google</a></div></div><div class=MEntry><div class=MFile><a href="../Layer/Google/v3-js.html">Google.v3</a></div></div><div class=MEntry><div class=MFile><a href="../Layer/Grid-js.html">Grid</a></div></div><div class=MEntry><div class=MFile><a href="../Layer/HTTPRequest-js.html">HTTPRequest</a></div></div><div class=MEntry><div class=MFile><a href="../Layer/Image-js.html">Image</a></div></div><div class=MEntry><div class=MFile><a href="../Layer/KaMap-js.html">KaMap</a></div></div><div class=MEntry><div class=MFile><a href="../Layer/KaMapCache-js.html">KaMapCache</a></div></div><div class=MEntry><div class=MFile><a href="../Layer/MapGuide-js.html">MapGuide</a></div></div><div class=MEntry><div class=MFile><a href="../Layer/MapServer-js.html">MapServer</a></div></div><div class=MEntry><div class=MFile><a href="../Layer/Markers-js.html">Markers</a></div></div><div class=MEntry><div class=MFile><a href="../Layer/OSM-js.html">OSM</a></div></div><div class=MEntry><div class=MFile><a href="../Layer/PointGrid-js.html">PointGrid</a></div></div><div class=MEntry><div class=MFile><a href="../Layer/PointTrack-js.html">PointTrack</a></div></div><div class=MEntry><div class=MFile><a href="../Layer/SphericalMercator-js.html">SphericalMercator</a></div></div><div class=MEntry><div class=MFile><a href="../Layer/Text-js.html">Text</a></div></div><div class=MEntry><div class=MFile><a href="../Layer/TileCache-js.html">TileCache</a></div></div><div class=MEntry><div class=MFile><a href="../Layer/TMS-js.html">TMS</a></div></div><div class=MEntry><div class=MFile><a href="../Layer/Vector-js.html">Vector</a></div></div><div class=MEntry><div class=MFile><a href="../Layer/Vector/RootContainer-js.html">Vector.<wbr>RootContainer</a></div></div><div class=MEntry><div class=MFile><a href="../Layer/WMS-js.html">WMS</a></div></div><div class=MEntry><div class=MFile><a href="../Layer/WMTS-js.html">WMTS</a></div></div><div class=MEntry><div class=MFile><a href="../Layer/WorldWind-js.html">WorldWind</a></div></div><div class=MEntry><div class=MFile><a href="../Layer/XYZ-js.html">XYZ</a></div></div><div class=MEntry><div class=MFile><a href="../Layer/Zoomify-js.html">Zoomify</a></div></div><div class=MEntry><div class=MFile><a href="../Layer/UTFGrid-js.html">UTFGrid</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent24')">Marker</a><div class=MGroupContent id=MGroupContent24><div class=MEntry><div class=MFile><a href="../Marker-js.html">Marker</a></div></div><div class=MEntry><div class=MFile><a href="../Marker/Box-js.html">Box</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent25')">Popup</a><div class=MGroupContent id=MGroupContent25><div class=MEntry><div class=MFile><a href="../Popup-js.html">Popup</a></div></div><div class=MEntry><div class=MFile><a href="../Popup/Anchored-js.html">Anchored</a></div></div><div class=MEntry><div class=MFile><a href="../Popup/Framed-js.html">Framed</a></div></div><div class=MEntry><div class=MFile><a href="../Popup/FramedCloud-js.html">FramedCloud</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent28')">Protocol</a><div class=MGroupContent id=MGroupContent28><div class=MEntry><div class=MFile><a href="../Protocol-js.html">Protocol</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent26')">Protocol</a><div class=MGroupContent id=MGroupContent26><div class=MEntry><div class=MFile><a href="../Protocol/CSW-js.html">CSW</a></div></div><div class=MEntry><div class=MFile><a href="../Protocol/CSW/v2_0_2-js.html">CSW.<wbr>v2_0_2</a></div></div><div class=MEntry><div class=MFile><a href="../Protocol/HTTP-js.html">HTTP</a></div></div><div class=MEntry><div class=MFile><a href="../Protocol/Script-js.html">Script</a></div></div><div class=MEntry><div class=MFile><a href="../Protocol/SOS-js.html">SOS.<wbr>DEFAULTS</a></div></div><div class=MEntry><div class=MFile><a href="../Protocol/SOS/v1_0_0-js.html">SOS.<wbr>v1_0_0</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent27')">WFS</a><div class=MGroupContent id=MGroupContent27><div class=MEntry><div class=MFile><a href="../Protocol/WFS-js.html">WFS</a></div></div><div class=MEntry><div class=MFile><a href="../Protocol/WFS/v1-js.html">v1</a></div></div><div class=MEntry><div class=MFile><a href="../Protocol/WFS/v1_0_0-js.html">v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="../Protocol/WFS/v1_1_0-js.html">v1_1_0</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent29')">Renderer</a><div class=MGroupContent id=MGroupContent29><div class=MEntry><div class=MFile><a href="../Renderer-js.html">Renderer</a></div></div><div class=MEntry><div class=MFile><a href="../Renderer/Canvas-js.html">Canvas</a></div></div><div class=MEntry><div class=MFile><a href="../Renderer/Elements-js.html">ElementsIndexer</a></div></div><div class=MEntry><div class=MFile><a href="../Renderer/SVG-js.html">SVG</a></div></div><div class=MEntry><div class=MFile><a href="../Renderer/VML-js.html">VML</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent30')">Request</a><div class=MGroupContent id=MGroupContent30><div class=MEntry><div class=MFile><a href="../Request-js.html">Request</a></div></div><div class=MEntry><div class=MFile><a href="../Request/XMLHttpRequest-js.html">XMLHttpRequest</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent31')">Strategy</a><div class=MGroupContent id=MGroupContent31><div class=MEntry><div class=MFile><a href="../Strategy-js.html">Strategy</a></div></div><div class=MEntry><div class=MFile><a href="../Strategy/BBOX-js.html">BBOX</a></div></div><div class=MEntry><div class=MFile><a href="../Strategy/Cluster-js.html">Cluster</a></div></div><div class=MEntry><div class=MFile><a href="../Strategy/Filter-js.html">Filter</a></div></div><div class=MEntry><div class=MFile><a href="../Strategy/Fixed-js.html">Fixed</a></div></div><div class=MEntry><div class=MFile><a href="../Strategy/Paging-js.html">Paging</a></div></div><div class=MEntry><div class=MFile><a href="../Strategy/Refresh-js.html">Refresh</a></div></div><div class=MEntry><div class=MFile><a href="../Strategy/Save-js.html">Save</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent32')">Symbolizer</a><div class=MGroupContent id=MGroupContent32><div class=MEntry><div class=MFile><a href="../Symbolizer-js.html">Symbolizer</a></div></div><div class=MEntry><div class=MFile><a href="../Symbolizer/Line-js.html">Line</a></div></div><div class=MEntry><div class=MFile><a href="../Symbolizer/Point-js.html">Point</a></div></div><div class=MEntry><div class=MFile><a href="../Symbolizer/Polygon-js.html">Polygon</a></div></div><div class=MEntry><div class=MFile><a href="../Symbolizer/Raster-js.html">Raster</a></div></div><div class=MEntry><div class=MFile><a href="../Symbolizer/Text-js.html">Text</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent33')">Tile</a><div class=MGroupContent id=MGroupContent33><div class=MEntry><div class=MFile><a href="../Tile-js.html">Tile</a></div></div><div class=MEntry><div class=MFile><a href="../Tile/Image-js.html">Image</a></div></div><div class=MEntry><div class=MFile><a href="../Tile/Image/IFrame-js.html">Image.<wbr>IFrame</a></div></div><div class=MEntry><div class=MFile><a href="../Tile/UTFGrid-js.html">UTFGrid</a></div></div></div></div></div><div class=MEntry><div class=MFile><a href="../../deprecated-js.html">Deprecated</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent34')">OpenLayers</a><div class=MGroupContent id=MGroupContent34><div class=MEntry><div class=MFile><a href="../Console-js.html">Console</a></div></div><div class=MEntry><div class=MFile><a href="../Events-js.html">Events</a></div></div><div class=MEntry><div class=MFile><a href="../Icon-js.html">Icon</a></div></div><div class=MEntry><div class=MFile><a href="../Map-js.html">Map</a></div></div><div class=MEntry><div class=MFile><a href="../Animation-js.html">OpenLayers.<wbr>Animation</a></div></div><div class=MEntry><div class=MFile><a href="../Events/buttonclick-js.html">OpenLayers.<wbr>Events.<wbr>buttonclick</a></div></div><div class=MEntry><div class=MFile><a href="../Events/featureclick-js.html">OpenLayers.<wbr>Events.<wbr>featureclick</a></div></div><div class=MEntry><div class=MFile><a href="../Kinetic-js.html">OpenLayers.<wbr>Kinetic</a></div></div><div class=MEntry><div class=MFile><a href="../TileManager-js.html">OpenLayers.<wbr>TileManager</a></div></div><div class=MEntry><div class=MFile><a href="../Util/vendorPrefix-js.html">OpenLayers.<wbr>Util.<wbr>vendorPrefix</a></div></div><div class=MEntry><div class=MFile><a href="../WPSClient-js.html">OpenLayers.<wbr>WPSClient</a></div></div><div class=MEntry><div class=MFile><a href="../WPSProcess-js.html">OpenLayers.<wbr>WPSProcess</a></div></div><div class=MEntry><div class=MFile><a href="../Projection-js.html">Projection</a></div></div><div class=MEntry><div class=MFile><a href="../Rule-js.html">Rule</a></div></div><div class=MEntry><div class=MFile><a href="../SingleFile-js.html">SingleFile.js</a></div></div><div class=MEntry><div class=MFile><a href="../Spherical-js.html">Spherical</a></div></div><div class=MEntry><div class=MFile><a href="../Style-js.html">Style</a></div></div><div class=MEntry><div class=MFile><a href="../Style2-js.html">Style2</a></div></div><div class=MEntry><div class=MFile><a href="../StyleMap-js.html">StyleMap</a></div></div><div class=MEntry><div class=MFile><a href="../Tween-js.html">Tween</a></div></div><div class=MEntry><div class=MFile><a href="../Util-js.html">Util</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent36')">Index</a><div class=MGroupContent id=MGroupContent36><div class=MEntry><div class=MIndex><a href="../../../index/General.html">Everything</a></div></div><div class=MEntry><div class=MIndex><a href="../../../index/Classes.html">Classes</a></div></div><div class=MEntry><div class=MIndex><a href="../../../index/Constants.html">Constants</a></div></div><div class=MEntry><div class=MIndex><a href="../../../index/Functions.html">Functions</a></div></div><div class=MEntry><div class=MIndex><a href="../../../index/Properties.html">Properties</a></div></div><div class=MEntry><div class=MIndex><a href="../../../index/Files.html">Files</a></div></div><div class=MEntry><div class=MIndex><a href="../../../index/Constructor.html">Constructor</a></div></div></div></div></div><script type="text/javascript"><!--
var searchPanel = new SearchPanel("searchPanel", "HTML", "../../../search");
--></script><div id=MSearchPanel class=MSearchPanelInactive><input type=text id=MSearchField value=Search onFocus="searchPanel.OnSearchFieldFocus(true)" onBlur="searchPanel.OnSearchFieldFocus(false)" onKeyUp="searchPanel.OnSearchFieldChange()"><select id=MSearchType onFocus="searchPanel.OnSearchTypeFocus(true)" onBlur="searchPanel.OnSearchTypeFocus(false)" onChange="searchPanel.OnSearchTypeChange()"><option  id=MSearchEverything selected value="General">Everything</option><option value="Classes">Classes</option><option value="Constants">Constants</option><option value="Constructor">Constructor</option><option value="Files">Files</option><option value="Functions">Functions</option><option value="Properties">Properties</option></select></div><script language=JavaScript><!--
HideAllBut([1, 35], 37);// --></script></div><!--Menu-->



<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt1"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">toString:function()</td></tr></table></blockquote>Returns a string representation of the bounds object.</div></div><div class=CToolTip id="tt2"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>toArray: function(</td><td class="PParameter  prettyprint " nowrap>reverseAxisOrder</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Returns an array representation of the bounds object.</div></div><div class=CToolTip id="tt3"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>toBBOX:function(</td><td class="PParameter  prettyprint " nowrap>decimal,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>reverseAxisOrder</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Returns a boundingbox-string representation of the bounds object.</div></div><div class=CToolTip id="tt4"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">toGeometry: function()</td></tr></table></blockquote>Create a new polygon geometry based on this bounds.</div></div><div class=CToolTip id="tt5"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">getWidth:function()</td></tr></table></blockquote>Returns the width of the bounds.</div></div><div class=CToolTip id="tt6"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">getHeight:function()</td></tr></table></blockquote>Returns the height of the bounds.</div></div><div class=CToolTip id="tt7"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">getSize:function()</td></tr></table></blockquote>Returns an OpenLayers.Size object of the bounds.</div></div><div class=CToolTip id="tt8"><div class=CClass>Instances of this class represent a width/height pair</div></div><div class=CToolTip id="tt9"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">getCenterPixel:function()</td></tr></table></blockquote>Returns the OpenLayers.Pixel object which represents the center of the bounds.</div></div><div class=CToolTip id="tt10"><div class=CClass>This class represents a screen coordinate, in x and y coordinates</div></div><div class=CToolTip id="tt11"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">getCenterLonLat:function()</td></tr></table></blockquote>Returns the OpenLayers.LonLat object which represents the center of the bounds.</div></div><div class=CToolTip id="tt12"><div class=CClass>This class represents a longitude and latitude pair</div></div><div class=CToolTip id="tt13"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>scale: function(</td><td class="PParameter  prettyprint " nowrap>ratio,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>origin</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Scales the bounds around a pixel or lonlat. </div></div><div class=CToolTip id="tt14"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>add:function(</td><td class="PParameter  prettyprint " nowrap>x,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>y</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Shifts the coordinates of the bound by the given horizontal and vertical deltas.</div></div><div class=CToolTip id="tt15"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>extend:function(</td><td class="PParameter  prettyprint " nowrap>object</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Extend the bounds to include the OpenLayers.LonLat, OpenLayers.Geometry.Point or OpenLayers.Bounds specified.</div></div><div class=CToolTip id="tt16"><div class=CClass>Point geometry class.</div></div><div class=CToolTip id="tt17"><div class=CConstructor>Construct a new bounds object. </div></div><div class=CToolTip id="tt18"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>extendXY:function(</td><td class="PParameter  prettyprint " nowrap>x,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>y</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Extend the bounds to include the XY coordinate specified.</div></div><div class=CToolTip id="tt19"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>containsLonLat: function(</td><td class="PParameter  prettyprint " nowrap>ll,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>options</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Returns whether the bounds object contains the given OpenLayers.LonLat.</div></div><div class=CToolTip id="tt20"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>containsPixel:function(</td><td class="PParameter  prettyprint " nowrap>px,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>inclusive</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Returns whether the bounds object contains the given OpenLayers.Pixel.</div></div><div class=CToolTip id="tt21"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>contains:function(</td><td class="PParameter  prettyprint " nowrap>x,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>y,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>inclusive</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Returns whether the bounds object contains the given x and y.</div></div><div class=CToolTip id="tt22"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>intersectsBounds:function(</td><td class="PParameter  prettyprint " nowrap>bounds,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>options</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Determine whether the target bounds intersects this bounds. </div></div><div class=CToolTip id="tt23"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>containsBounds:function(</td><td class="PParameter  prettyprint " nowrap>bounds,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>partial,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>inclusive</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Returns whether the bounds object contains the given OpenLayers.Bounds.</div></div><div class=CToolTip id="tt24"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>determineQuadrant: function(</td><td class="PParameter  prettyprint " nowrap>lonlat</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Returns the the quadrant (&ldquo;br&rdquo;, &ldquo;tr&rdquo;, &ldquo;tl&rdquo;, &ldquo;bl&rdquo;) in which the given OpenLayers.LonLat lies.</div></div><div class=CToolTip id="tt25"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>transform: function(</td><td class="PParameter  prettyprint " nowrap>source,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>dest</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Transform the Bounds object from source to dest.</div></div><div class=CToolTip id="tt26"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>OpenLayers.Bounds.fromString = function(</td><td class="PParameter  prettyprint " nowrap>str,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>reverseAxisOrder</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Alternative constructor that builds a new OpenLayers.Bounds from a parameter string.</div></div><div class=CToolTip id="tt27"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>OpenLayers.Bounds.fromArray = function(</td><td class="PParameter  prettyprint " nowrap>bbox,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>reverseAxisOrder</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Alternative constructor that builds a new OpenLayers.Bounds from an array.</div></div><div class=CToolTip id="tt28"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>OpenLayers.Bounds.fromSize = function(</td><td class="PParameter  prettyprint " nowrap>size</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Alternative constructor that builds a new OpenLayers.Bounds from a size.</div></div><div class=CToolTip id="tt29"><div class=CClass>Polygon is a collection of Geometry.LinearRings.</div></div><div class=CToolTip id="tt30"><div class=CClass>Methods for coordinate transforms between coordinate systems. </div></div><!--END_ND_TOOLTIPS-->




<div id=MSearchResultsWindow><iframe src="" frameborder=0 name=MSearchResults id=MSearchResults></iframe><a href="javascript:searchPanel.CloseResultsWindow()" id=MSearchResultsWindowClose>Close</a></div>


<script language=JavaScript><!--
if (browserType) {if (browserVer) {document.write("</div>"); }document.write("</div>");}// --></script></body></html>