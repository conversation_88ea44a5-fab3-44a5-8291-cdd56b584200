<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0//EN" "http://www.w3.org/TR/REC-html40/strict.dtd">

<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><title>Index - OpenLayers</title><link rel="stylesheet" type="text/css" href="../styles/main.css"><script language=JavaScript src="../javascript/main.js"></script><script language=JavaScript src="../javascript/searchdata.js"></script></head><body class="IndexPage" onLoad="NDOnLoad()"><script language=JavaScript><!--
if (browserType) {document.write("<div class=" + browserType + ">");if (browserVer) {document.write("<div class=" + browserVer + ">"); }}// --></script>

<!--  Generated by Natural Docs, version 1.51 -->
<!--  http://www.naturaldocs.org  -->

<!-- saved from url=(0026)http://www.naturaldocs.org -->




<div id=Index><div class=IPageTitle>Index</div><div class=INavigationBar><a href="General.html#Symbols">$#!</a> &middot; 0-9 &middot; <a href="General.html#A">A</a> &middot; <a href="General2.html#B">B</a> &middot; <a href="General3.html#C">C</a> &middot; <a href="General4.html#D">D</a> &middot; <a href="General5.html#E">E</a> &middot; <a href="General6.html#F">F</a> &middot; <a href="General7.html#G">G</a> &middot; <a href="General8.html#H">H</a> &middot; <a href="#I">I</a> &middot; <a href="General10.html#J">J</a> &middot; <a href="General10.html#K">K</a> &middot; <a href="General11.html#L">L</a> &middot; <a href="General12.html#M">M</a> &middot; <a href="General13.html#N">N</a> &middot; <a href="General14.html#O">O</a> &middot; <a href="General15.html#P">P</a> &middot; <a href="General16.html#Q">Q</a> &middot; <a href="General17.html#R">R</a> &middot; <a href="General18.html#S">S</a> &middot; <a href="General19.html#T">T</a> &middot; <a href="General20.html#U">U</a> &middot; <a href="General21.html#V">V</a> &middot; <a href="General22.html#W">W</a> &middot; <a href="General23.html#X">X</a> &middot; <a href="General23.html#Y">Y</a> &middot; <a href="General23.html#Z">Z</a></div><table border=0 cellspacing=0 cellpadding=0><tr><td class=IHeading id=IFirstHeading><a name="I"></a>I</td><td></td></tr><tr><td class=ISymbolPrefix id=IFirstSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Lang-js.html#OpenLayers.Lang.OpenLayers.i18n" id=link1755 onMouseOver="ShowTip(event, 'tt1755', 'link1755')" onMouseOut="HideTip('tt1755')" class=ISymbol>i18n</a>, <span class=IParent>OpenLayers.<wbr>Lang.<wbr>OpenLayers</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>icon</span><div class=ISubIndex><a href="../files/OpenLayers/Layer/GeoRSS-js.html#OpenLayers.Layer.GeoRSS.icon" id=link1756 onMouseOver="ShowTip(event, 'tt1756', 'link1756')" onMouseOut="HideTip('tt1756')" class=IParent>OpenLayers.<wbr>Layer.<wbr>GeoRSS</a><a href="../files/OpenLayers/Marker-js.html#OpenLayers.Marker.icon" id=link1757 onMouseOver="ShowTip(event, 'tt1757', 'link1757')" onMouseOut="HideTip('tt1757')" class=IParent>OpenLayers.<wbr>Marker</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Icon-js.html#OpenLayers.Icon.OpenLayers.Icon" id=link1758 onMouseOver="ShowTip(event, 'tt1758', 'link1758')" onMouseOut="HideTip('tt1758')" class=ISymbol>Icon</a>, <span class=IParent>OpenLayers.<wbr>Icon.<wbr>OpenLayers</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Control/Panel-js.html#OpenLayers.Control.Panel.iconOff" id=link1759 onMouseOver="ShowTip(event, 'tt1759', 'link1759')" onMouseOut="HideTip('tt1759')" class=ISymbol>iconOff</a>, <span class=IParent>OpenLayers.<wbr>Control.<wbr>Panel</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Control/Panel-js.html#OpenLayers.Control.Panel.iconOn" id=link1760 onMouseOver="ShowTip(event, 'tt1760', 'link1760')" onMouseOut="HideTip('tt1760')" class=ISymbol>iconOn</a>, <span class=IParent>OpenLayers.<wbr>Control.<wbr>Panel</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>id</span><div class=ISubIndex><a href="../files/OpenLayers/Control-js.html#OpenLayers.Control.id" id=link1761 onMouseOver="ShowTip(event, 'tt1761', 'link1761')" onMouseOut="HideTip('tt1761')" class=IParent>OpenLayers.<wbr>Control</a><a href="../files/OpenLayers/Feature-js.html#OpenLayers.Feature.id" id=link1762 onMouseOver="ShowTip(event, 'tt1762', 'link1762')" onMouseOut="HideTip('tt1762')" class=IParent>OpenLayers.<wbr>Feature</a><a href="../files/OpenLayers/Geometry-js.html#OpenLayers.Geometry.id" id=link1763 onMouseOver="ShowTip(event, 'tt1763', 'link1763')" onMouseOut="HideTip('tt1763')" class=IParent>OpenLayers.<wbr>Geometry</a><a href="../files/OpenLayers/Handler-js.html#OpenLayers.Handler.id" id=link1764 onMouseOver="ShowTip(event, 'tt1764', 'link1764')" onMouseOut="HideTip('tt1764')" class=IParent>OpenLayers.<wbr>Handler</a><a href="../files/OpenLayers/Layer-js.html#OpenLayers.Layer.id" id=link1765 onMouseOver="ShowTip(event, 'tt1765', 'link1765')" onMouseOut="HideTip('tt1765')" class=IParent>OpenLayers.<wbr>Layer</a><a href="../files/OpenLayers/Map-js.html#OpenLayers.Map.id" id=link1766 onMouseOver="ShowTip(event, 'tt1766', 'link1766')" onMouseOut="HideTip('tt1766')" class=IParent>OpenLayers.Map</a><a href="../files/OpenLayers/Popup-js.html#OpenLayers.Popup.id" id=link1767 onMouseOver="ShowTip(event, 'tt1767', 'link1767')" onMouseOut="HideTip('tt1767')" class=IParent>OpenLayers.<wbr>Popup</a><a href="../files/OpenLayers/Rule-js.html#OpenLayers.Rule.id" id=link1768 onMouseOver="ShowTip(event, 'tt1768', 'link1768')" onMouseOut="HideTip('tt1768')" class=IParent>OpenLayers.Rule</a><a href="../files/OpenLayers/Style-js.html#OpenLayers.Style.id" id=link1769 onMouseOver="ShowTip(event, 'tt1769', 'link1769')" onMouseOut="HideTip('tt1769')" class=IParent>OpenLayers.<wbr>Style</a><a href="../files/OpenLayers/Style2-js.html#OpenLayers.Style2.id" id=link1770 onMouseOver="ShowTip(event, 'tt1770', 'link1770')" onMouseOut="HideTip('tt1770')" class=IParent>OpenLayers.<wbr>Style2</a><a href="../files/OpenLayers/Tile-js.html#OpenLayers.Tile.id" id=link1771 onMouseOver="ShowTip(event, 'tt1771', 'link1771')" onMouseOut="HideTip('tt1771')" class=IParent>OpenLayers.Tile</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/WPSProcess-js.html#OpenLayers.WPSProcess.identifier" id=link1772 onMouseOver="ShowTip(event, 'tt1772', 'link1772')" onMouseOut="HideTip('tt1772')" class=ISymbol>identifier</a>, <span class=IParent>OpenLayers.<wbr>WPSProcess</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Tile/Image/IFrame-js.html#OpenLayers.Tile.Image.IFrame" id=link1773 onMouseOver="ShowTip(event, 'tt1773', 'link1773')" onMouseOut="HideTip('tt1773')" class=ISymbol>IFrame</a>, <span class=IParent>OpenLayers.<wbr>Tile.<wbr>Image</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Tile/Image/IFrame-js.html#IFrame.js"  class=ISymbol>IFrame.js</a></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Events/buttonclick-js.html#OpenLayers.Events.buttonclick.ignore" id=link1774 onMouseOver="ShowTip(event, 'tt1774', 'link1774')" onMouseOut="HideTip('tt1774')" class=ISymbol>ignore</a>, <span class=IParent>OpenLayers.<wbr>Events.<wbr>buttonclick</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Format/GeoJSON-js.html#OpenLayers.Format.GeoJSON.ignoreExtraDims" id=link1775 onMouseOver="ShowTip(event, 'tt1775', 'link1775')" onMouseOut="HideTip('tt1775')" class=ISymbol>ignoreExtraDims</a>, <span class=IParent>OpenLayers.<wbr>Format.<wbr>GeoJSON</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>Image</span><div class=ISubIndex><a href="../files/OpenLayers/Layer/Image-js.html#OpenLayers.Layer.Image.OpenLayers.Layer.Image" id=link1776 onMouseOver="ShowTip(event, 'tt1776', 'link1776')" onMouseOut="HideTip('tt1776')" class=IParent>OpenLayers.<wbr>Layer.<wbr>Image.<wbr>OpenLayers.<wbr>Layer</a><a href="../files/OpenLayers/Tile/Image-js.html#OpenLayers.Tile.Image.OpenLayers.Tile.Image" id=link1777 onMouseOver="ShowTip(event, 'tt1777', 'link1777')" onMouseOut="HideTip('tt1777')" class=IParent>OpenLayers.<wbr>Tile.<wbr>Image.<wbr>OpenLayers.Tile</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Tile/Image-js.html#OpenLayers.Tile.Image.OpenLayers.Tile.Image.IMAGE" id=link1778 onMouseOver="ShowTip(event, 'tt1778', 'link1778')" onMouseOut="HideTip('tt1778')" class=ISymbol>IMAGE</a>, <span class=IParent>OpenLayers.<wbr>Tile.<wbr>Image.<wbr>OpenLayers.<wbr>Tile.<wbr>Image</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Layer/KaMapCache-js.html#OpenLayers.Layer.KaMapCache.IMAGE_EXTENSIONS" id=link1779 onMouseOver="ShowTip(event, 'tt1779', 'link1779')" onMouseOut="HideTip('tt1779')" class=ISymbol>IMAGE_EXTENSIONS</a>, <span class=IParent>OpenLayers.<wbr>Layer.<wbr>KaMapCache</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Util-js.html#Util.IMAGE_RELOAD_ATTEMPTS" id=link1780 onMouseOver="ShowTip(event, 'tt1780', 'link1780')" onMouseOut="HideTip('tt1780')" class=ISymbol>IMAGE_RELOAD_ATTEMPTS</a>, <span class=IParent>Util</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Icon-js.html#OpenLayers.Icon.imageDiv" id=link1781 onMouseOver="ShowTip(event, 'tt1781', 'link1781')" onMouseOut="HideTip('tt1781')" class=ISymbol>imageDiv</a>, <span class=IParent>OpenLayers.Icon</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Control/CacheWrite-js.html#OpenLayers.Control.CacheWrite.imageFormat" id=link1782 onMouseOver="ShowTip(event, 'tt1782', 'link1782')" onMouseOut="HideTip('tt1782')" class=ISymbol>imageFormat</a>, <span class=IParent>OpenLayers.<wbr>Control.<wbr>CacheWrite</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Tile/Image-js.html#OpenLayers.Tile.Image.imageReloadAttempts" id=link1783 onMouseOver="ShowTip(event, 'tt1783', 'link1783')" onMouseOut="HideTip('tt1783')" class=ISymbol>imageReloadAttempts</a>, <span class=IParent>OpenLayers.<wbr>Tile.<wbr>Image</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>imageSize</span><div class=ISubIndex><a href="../files/OpenLayers/Popup/Framed-js.html#OpenLayers.Popup.Framed.imageSize" id=link1784 onMouseOver="ShowTip(event, 'tt1784', 'link1784')" onMouseOut="HideTip('tt1784')" class=IParent>OpenLayers.<wbr>Popup.<wbr>Framed</a><a href="../files/OpenLayers/Popup/FramedCloud-js.html#OpenLayers.Popup.FramedCloud.imageSize" id=link1785 onMouseOver="ShowTip(event, 'tt1785', 'link1785')" onMouseOut="HideTip('tt1785')" class=IParent>OpenLayers.<wbr>Popup.<wbr>FramedCloud</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Popup/Framed-js.html#OpenLayers.Popup.Framed.imageSrc" id=link1786 onMouseOver="ShowTip(event, 'tt1786', 'link1786')" onMouseOut="HideTip('tt1786')" class=ISymbol>imageSrc</a>, <span class=IParent>OpenLayers.<wbr>Popup.<wbr>Framed</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Tile/Image-js.html#OpenLayers.Tile.Image.imgDiv" id=link1787 onMouseOver="ShowTip(event, 'tt1787', 'link1787')" onMouseOut="HideTip('tt1787')" class=ISymbol>imgDiv</a>, <span class=IParent>OpenLayers.<wbr>Tile.<wbr>Image</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>ImgPath</span><div class=ISubIndex><a href="../files/OpenLayers/SingleFile-js.html#ImgPath" id=link1788 onMouseOver="ShowTip(event, 'tt1788', 'link1788')" onMouseOut="HideTip('tt1788')" class=IParent>Global</a><a href="../files/OpenLayers-js.html#OpenLayers.ImgPath" id=link1789 onMouseOver="ShowTip(event, 'tt1789', 'link1789')" onMouseOut="HideTip('tt1789')" class=IParent>OpenLayers</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Control/Measure-js.html#OpenLayers.Control.Measure.immediate" id=link1790 onMouseOver="ShowTip(event, 'tt1790', 'link1790')" onMouseOut="HideTip('tt1790')" class=ISymbol>immediate</a>, <span class=IParent>OpenLayers.<wbr>Control.<wbr>Measure</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>importSymbol</span><div class=ISubIndex><a href="../files/OpenLayers/Renderer/SVG-js.html#OpenLayers.Renderer.SVG.importSymbol" id=link1791 onMouseOver="ShowTip(event, 'tt1791', 'link1791')" onMouseOut="HideTip('tt1791')" class=IParent>OpenLayers.<wbr>Renderer.SVG</a><a href="../files/deprecated-js.html#OpenLayers.Renderer.SVG2.importSymbol" id=link1792 onMouseOver="ShowTip(event, 'tt1792', 'link1792')" onMouseOut="HideTip('tt1792')" class=IParent>OpenLayers.<wbr>Renderer.SVG2</a><a href="../files/OpenLayers/Renderer/VML-js.html#OpenLayers.Renderer.VML.importSymbol" id=link1793 onMouseOver="ShowTip(event, 'tt1793', 'link1793')" onMouseOut="HideTip('tt1793')" class=IParent>OpenLayers.<wbr>Renderer.VML</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Util-js.html#Util.INCHES_PER_UNIT" id=link1794 onMouseOver="ShowTip(event, 'tt1794', 'link1794')" onMouseOut="HideTip('tt1794')" class=ISymbol>INCHES_PER_UNIT</a>, <span class=IParent>Util</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Events-js.html#OpenLayers.Events.includeXY" id=link1795 onMouseOver="ShowTip(event, 'tt1795', 'link1795')" onMouseOut="HideTip('tt1795')" class=ISymbol>includeXY</a>, <span class=IParent>OpenLayers.<wbr>Events</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Format/JSON-js.html#OpenLayers.Format.JSON.indent" id=link1796 onMouseOver="ShowTip(event, 'tt1796', 'link1796')" onMouseOut="HideTip('tt1796')" class=ISymbol>indent</a>, <span class=IParent>OpenLayers.<wbr>Format.JSON</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Renderer/Elements-js.html#OpenLayers.Renderer.Elements.Indexer" id=link1797 onMouseOver="ShowTip(event, 'tt1797', 'link1797')" onMouseOut="HideTip('tt1797')" class=ISymbol>Indexer</a>, <span class=IParent>OpenLayers.<wbr>Renderer.<wbr>Elements</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Tile/UTFGrid-js.html#OpenLayers.Tile.UTFGrid.indexFromCharCode" id=link1798 onMouseOver="ShowTip(event, 'tt1798', 'link1798')" onMouseOut="HideTip('tt1798')" class=ISymbol>indexFromCharCode</a>, <span class=IParent>OpenLayers.<wbr>Tile.<wbr>UTFGrid</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Util-js.html#Util.indexOf" id=link1799 onMouseOver="ShowTip(event, 'tt1799', 'link1799')" onMouseOut="HideTip('tt1799')" class=ISymbol>indexOf</a>, <span class=IParent>Util</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Renderer/Elements-js.html#OpenLayers.ElementsIndexer.indices" id=link1800 onMouseOver="ShowTip(event, 'tt1800', 'link1800')" onMouseOut="HideTip('tt1800')" class=ISymbol>indices</a>, <span class=IParent>OpenLayers.<wbr>ElementsIndexer</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Marker-js.html#OpenLayers.Marker.inflate" id=link1801 onMouseOver="ShowTip(event, 'tt1801', 'link1801')" onMouseOut="HideTip('tt1801')" class=ISymbol>inflate</a>, <span class=IParent>OpenLayers.<wbr>Marker</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Console-js.html#OpenLayers.Console.info" id=link1802 onMouseOver="ShowTip(event, 'tt1802', 'link1802')" onMouseOut="HideTip('tt1802')" class=ISymbol>info</a>, <span class=IParent>OpenLayers.<wbr>Console</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>infoFormat</span><div class=ISubIndex><a href="../files/OpenLayers/Control/WMSGetFeatureInfo-js.html#OpenLayers.Control.WMSGetFeatureInfo.infoFormat" id=link1803 onMouseOver="ShowTip(event, 'tt1803', 'link1803')" onMouseOut="HideTip('tt1803')" class=IParent>OpenLayers.<wbr>Control.<wbr>WMSGetFeatureInfo</a><a href="../files/OpenLayers/Control/WMTSGetFeatureInfo-js.html#OpenLayers.Control.WMTSGetFeatureInfo.infoFormat" id=link1804 onMouseOver="ShowTip(event, 'tt1804', 'link1804')" onMouseOut="HideTip('tt1804')" class=IParent>OpenLayers.<wbr>Control.<wbr>WMTSGetFeatureInfo</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>inherit</span><div class=ISubIndex><a href="../files/OpenLayers/BaseTypes/Class-js.html#OpenLayers.inherit" id=link1805 onMouseOver="ShowTip(event, 'tt1805', 'link1805')" onMouseOut="HideTip('tt1805')" class=IParent>OpenLayers</a><a href="../files/deprecated-js.html#OpenLayers.Class.inherit" id=link1806 onMouseOver="ShowTip(event, 'tt1806', 'link1806')" onMouseOut="HideTip('tt1806')" class=IParent>OpenLayers.<wbr>Class</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Format/Atom-js.html#OpenLayers.Format.Atom.initGmlParser" id=link1807 onMouseOver="ShowTip(event, 'tt1807', 'link1807')" onMouseOut="HideTip('tt1807')" class=ISymbol>initGmlParser</a>, <span class=IParent>OpenLayers.<wbr>Format.Atom</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>initGriddedTiles</span><div class=ISubIndex><a href="../files/OpenLayers/Layer/ArcGISCache-js.html#initGriddedTiles" id=link1808 onMouseOver="ShowTip(event, 'tt1808', 'link1808')" onMouseOut="HideTip('tt1808')" class=IParent>Global</a><a href="../files/OpenLayers/Layer/Grid-js.html#OpenLayers.Layer.Grid.initGriddedTiles" id=link1809 onMouseOver="ShowTip(event, 'tt1809', 'link1809')" onMouseOut="HideTip('tt1809')" class=IParent>OpenLayers.<wbr>Layer.Grid</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Renderer/Elements-js.html#OpenLayers.ElementsIndexer.initialize" id=link1810 onMouseOver="ShowTip(event, 'tt1810', 'link1810')" onMouseOut="HideTip('tt1810')" class=ISymbol>initialize</a>, <span class=IParent>OpenLayers.<wbr>ElementsIndexer</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/deprecated-js.html#OpenLayers.Protocol.SQL.Gears.initializeDatabase" id=link1811 onMouseOver="ShowTip(event, 'tt1811', 'link1811')" onMouseOut="HideTip('tt1811')" class=ISymbol>initializeDatabase</a>, <span class=IParent>OpenLayers.<wbr>Protocol.<wbr>SQL.<wbr>Gears</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Layer/Zoomify-js.html#OpenLayers.Layer.Zoomify.initializeZoomify" id=link1812 onMouseOver="ShowTip(event, 'tt1812', 'link1812')" onMouseOut="HideTip('tt1812')" class=ISymbol>initializeZoomify</a>, <span class=IParent>OpenLayers.<wbr>Layer.<wbr>Zoomify</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Tile/Image-js.html#OpenLayers.Tile.Image.initImage" id=link1813 onMouseOver="ShowTip(event, 'tt1813', 'link1813')" onMouseOut="HideTip('tt1813')" class=ISymbol>initImage</a>, <span class=IParent>OpenLayers.<wbr>Tile.<wbr>Image</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>initLayer</span><div class=ISubIndex><a href="../files/OpenLayers/Control/SelectFeature-js.html#OpenLayers.Control.SelectFeature.initLayer" id=link1814 onMouseOver="ShowTip(event, 'tt1814', 'link1814')" onMouseOut="HideTip('tt1814')" class=IParent>OpenLayers.<wbr>Control.<wbr>SelectFeature</a><a href="../files/OpenLayers/Layer/Bing-js.html#OpenLayers.Layer.Bing.initLayer" id=link1815 onMouseOver="ShowTip(event, 'tt1815', 'link1815')" onMouseOut="HideTip('tt1815')" class=IParent>OpenLayers.<wbr>Layer.Bing</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Layer/SphericalMercator-js.html#OpenLayers.Layer.SphericalMercator.initMercatorParameters" id=link1816 onMouseOver="ShowTip(event, 'tt1816', 'link1816')" onMouseOut="HideTip('tt1816')" class=ISymbol>initMercatorParameters</a>, <span class=IParent>OpenLayers.<wbr>Layer.<wbr>SphericalMercator</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Layer/Grid-js.html#OpenLayers.Layer.Grid.initProperties" id=link1817 onMouseOver="ShowTip(event, 'tt1817', 'link1817')" onMouseOut="HideTip('tt1817')" class=ISymbol>initProperties</a>, <span class=IParent>OpenLayers.<wbr>Layer.Grid</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>initResolutions</span><div class=ISubIndex><a href="../files/OpenLayers/Layer-js.html#OpenLayers.Layer.initResolutions" id=link1818 onMouseOver="ShowTip(event, 'tt1818', 'link1818')" onMouseOut="HideTip('tt1818')" class=IParent>OpenLayers.<wbr>Layer</a><a href="../files/OpenLayers/Layer/FixedZoomLevels-js.html#OpenLayers.Layer.FixedZoomLevels.initResolutions" id=link1819 onMouseOver="ShowTip(event, 'tt1819', 'link1819')" onMouseOut="HideTip('tt1819')" class=IParent>OpenLayers.<wbr>Layer.<wbr>FixedZoomLevels</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Layer/Grid-js.html#OpenLayers.Layer.Grid.initSingleTile" id=link1820 onMouseOver="ShowTip(event, 'tt1820', 'link1820')" onMouseOut="HideTip('tt1820')" class=ISymbol>initSingleTile</a>, <span class=IParent>OpenLayers.<wbr>Layer.Grid</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Control/NavigationHistory-js.html#OpenLayers.Control.NavigationHistory.initStack" id=link1821 onMouseOver="ShowTip(event, 'tt1821', 'link1821')" onMouseOut="HideTip('tt1821')" class=ISymbol>initStack</a>, <span class=IParent>OpenLayers.<wbr>Control.<wbr>NavigationHistory</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Layer-js.html#OpenLayers.Layer.inRange" id=link1822 onMouseOver="ShowTip(event, 'tt1822', 'link1822')" onMouseOut="HideTip('tt1822')" class=ISymbol>inRange</a>, <span class=IParent>OpenLayers.<wbr>Layer</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>insert</span><div class=ISubIndex><a href="../files/OpenLayers/Renderer/Elements-js.html#OpenLayers.ElementsIndexer.insert" id=link1823 onMouseOver="ShowTip(event, 'tt1823', 'link1823')" onMouseOut="HideTip('tt1823')" class=IParent>OpenLayers.<wbr>ElementsIndexer</a><a href="../files/OpenLayers/Format/WFS-js.html#OpenLayers.Format.WFS.insert" id=link1824 onMouseOver="ShowTip(event, 'tt1824', 'link1824')" onMouseOut="HideTip('tt1824')" class=IParent>OpenLayers.<wbr>Format.WFS</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>insertDeflectionLength</span><div class=ISubIndex><a href="../files/OpenLayers/Control/DrawFeature-js.html#OpenLayers.Control.DrawFeature.insertDeflectionLength" id=link1825 onMouseOver="ShowTip(event, 'tt1825', 'link1825')" onMouseOut="HideTip('tt1825')" class=IParent>OpenLayers.<wbr>Control.<wbr>DrawFeature</a><a href="../files/OpenLayers/Handler/Path-js.html#OpenLayers.Handler.Path.insertDeflectionLength" id=link1826 onMouseOver="ShowTip(event, 'tt1826', 'link1826')" onMouseOut="HideTip('tt1826')" class=IParent>OpenLayers.<wbr>Handler.Path</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>insertDeltaXY</span><div class=ISubIndex><a href="../files/OpenLayers/Control/DrawFeature-js.html#OpenLayers.Control.DrawFeature.insertDeltaXY" id=link1827 onMouseOver="ShowTip(event, 'tt1827', 'link1827')" onMouseOut="HideTip('tt1827')" class=IParent>OpenLayers.<wbr>Control.<wbr>DrawFeature</a><a href="../files/OpenLayers/Handler/Path-js.html#OpenLayers.Handler.Path.insertDeltaXY" id=link1828 onMouseOver="ShowTip(event, 'tt1828', 'link1828')" onMouseOut="HideTip('tt1828')" class=IParent>OpenLayers.<wbr>Handler.Path</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>insertDirectionLength</span><div class=ISubIndex><a href="../files/OpenLayers/Control/DrawFeature-js.html#OpenLayers.Control.DrawFeature.insertDirectionLength" id=link1829 onMouseOver="ShowTip(event, 'tt1829', 'link1829')" onMouseOut="HideTip('tt1829')" class=IParent>OpenLayers.<wbr>Control.<wbr>DrawFeature</a><a href="../files/OpenLayers/Handler/Path-js.html#OpenLayers.Handler.Path.insertDirectionLength" id=link1830 onMouseOver="ShowTip(event, 'tt1830', 'link1830')" onMouseOut="HideTip('tt1830')" class=IParent>OpenLayers.<wbr>Handler.Path</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>insertXY</span><div class=ISubIndex><a href="../files/OpenLayers/Control/DrawFeature-js.html#OpenLayers.Control.DrawFeature.insertXY" id=link1831 onMouseOver="ShowTip(event, 'tt1831', 'link1831')" onMouseOut="HideTip('tt1831')" class=IParent>OpenLayers.<wbr>Control.<wbr>DrawFeature</a><a href="../files/OpenLayers/Handler/Path-js.html#OpenLayers.Handler.Path.insertXY" id=link1832 onMouseOver="ShowTip(event, 'tt1832', 'link1832')" onMouseOut="HideTip('tt1832')" class=IParent>OpenLayers.<wbr>Handler.Path</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Format/OSM-js.html#OpenLayers.Format.OSM.interestingTagsExclude" id=link1833 onMouseOver="ShowTip(event, 'tt1833', 'link1833')" onMouseOut="HideTip('tt1833')" class=ISymbol>interestingTagsExclude</a>, <span class=IParent>OpenLayers.<wbr>Format.OSM</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Format/KML-js.html#OpenLayers.Format.KML.internalns" id=link1834 onMouseOver="ShowTip(event, 'tt1834', 'link1834')" onMouseOut="HideTip('tt1834')" class=ISymbol>internalns</a>, <span class=IParent>OpenLayers.<wbr>Format.KML</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Format-js.html#OpenLayers.Format.internalProjection" id=link1835 onMouseOver="ShowTip(event, 'tt1835', 'link1835')" onMouseOut="HideTip('tt1835')" class=ISymbol>internalProjection</a>, <span class=IParent>OpenLayers.<wbr>Format</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>intersects</span><div class=ISubIndex><a href="../files/OpenLayers/Geometry/Collection-js.html#OpenLayers.Geometry.Collection.intersects" id=link1836 onMouseOver="ShowTip(event, 'tt1836', 'link1836')" onMouseOut="HideTip('tt1836')" class=IParent>OpenLayers.<wbr>Geometry.<wbr>Collection</a><a href="../files/OpenLayers/Geometry/LinearRing-js.html#OpenLayers.Geometry.LinearRing.intersects" id=link1837 onMouseOver="ShowTip(event, 'tt1837', 'link1837')" onMouseOut="HideTip('tt1837')" class=IParent>OpenLayers.<wbr>Geometry.<wbr>LinearRing</a><a href="../files/OpenLayers/Geometry/LineString-js.html#OpenLayers.Geometry.LineString.intersects" id=link1838 onMouseOver="ShowTip(event, 'tt1838', 'link1838')" onMouseOut="HideTip('tt1838')" class=IParent>OpenLayers.<wbr>Geometry.<wbr>LineString</a><a href="../files/OpenLayers/Geometry/Point-js.html#OpenLayers.Geometry.Point.intersects" id=link1839 onMouseOver="ShowTip(event, 'tt1839', 'link1839')" onMouseOut="HideTip('tt1839')" class=IParent>OpenLayers.<wbr>Geometry.<wbr>Point</a><a href="../files/OpenLayers/Geometry/Polygon-js.html#OpenLayers.Geometry.Polygon.intersects" id=link1840 onMouseOver="ShowTip(event, 'tt1840', 'link1840')" onMouseOut="HideTip('tt1840')" class=IParent>OpenLayers.<wbr>Geometry.<wbr>Polygon</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/BaseTypes/Bounds-js.html#OpenLayers.Bounds.intersectsBounds" id=link1841 onMouseOver="ShowTip(event, 'tt1841', 'link1841')" onMouseOut="HideTip('tt1841')" class=ISymbol>intersectsBounds</a>, <span class=IParent>OpenLayers.<wbr>Bounds</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>interval</span><div class=ISubIndex><a href="../files/OpenLayers/Control/DragPan-js.html#OpenLayers.Control.DragPan.interval" id=link1842 onMouseOver="ShowTip(event, 'tt1842', 'link1842')" onMouseOut="HideTip('tt1842')" class=IParent>OpenLayers.<wbr>Control.<wbr>DragPan</a><a href="../files/OpenLayers/Handler/Drag-js.html#OpenLayers.Handler.Drag.interval" id=link1843 onMouseOver="ShowTip(event, 'tt1843', 'link1843')" onMouseOut="HideTip('tt1843')" class=IParent>OpenLayers.<wbr>Handler.Drag</a><a href="../files/OpenLayers/Handler/MouseWheel-js.html#OpenLayers.Handler.MouseWheel.interval" id=link1844 onMouseOver="ShowTip(event, 'tt1844', 'link1844')" onMouseOut="HideTip('tt1844')" class=IParent>OpenLayers.<wbr>Handler.<wbr>MouseWheel</a><a href="../files/OpenLayers/Strategy/Refresh-js.html#OpenLayers.Strategy.Refresh.interval" id=link1845 onMouseOver="ShowTip(event, 'tt1845', 'link1845')" onMouseOut="HideTip('tt1845')" class=IParent>OpenLayers.<wbr>Strategy.<wbr>Refresh</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Control/Graticule-js.html#OpenLayers.Control.Graticule.intervals" id=link1846 onMouseOver="ShowTip(event, 'tt1846', 'link1846')" onMouseOut="HideTip('tt1846')" class=ISymbol>intervals</a>, <span class=IParent>OpenLayers.<wbr>Control.<wbr>Graticule</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>invalidBounds</span><div class=ISubIndex><a href="../files/OpenLayers/Layer/PointGrid-js.html#OpenLayers.Layer.PointGrid.invalidBounds" id=link1847 onMouseOver="ShowTip(event, 'tt1847', 'link1847')" onMouseOut="HideTip('tt1847')" class=IParent>OpenLayers.<wbr>Layer.<wbr>PointGrid</a><a href="../files/OpenLayers/Strategy/BBOX-js.html#OpenLayers.Strategy.BBOX.invalidBounds" id=link1848 onMouseOver="ShowTip(event, 'tt1848', 'link1848')" onMouseOut="HideTip('tt1848')" class=IParent>OpenLayers.<wbr>Strategy.BBOX</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Renderer/SVG-js.html#OpenLayers.Renderer.SVG.inValidRange" id=link1849 onMouseOver="ShowTip(event, 'tt1849', 'link1849')" onMouseOut="HideTip('tt1849')" class=ISymbol>inValidRange</a>, <span class=IParent>OpenLayers.<wbr>Renderer.SVG</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Layer/SphericalMercator-js.html#OpenLayers.Layer.SphericalMercator.inverseMercator" id=link1850 onMouseOver="ShowTip(event, 'tt1850', 'link1850')" onMouseOut="HideTip('tt1850')" class=ISymbol>inverseMercator</a>, <span class=IParent>OpenLayers.<wbr>Layer.<wbr>SphericalMercator</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>irregular</span><div class=ISubIndex><a href="../files/OpenLayers/Control/TransformFeature-js.html#OpenLayers.Control.TransformFeature.irregular" id=link1851 onMouseOver="ShowTip(event, 'tt1851', 'link1851')" onMouseOut="HideTip('tt1851')" class=IParent>OpenLayers.<wbr>Control.<wbr>TransformFeature</a><a href="../files/OpenLayers/Handler/RegularPolygon-js.html#OpenLayers.Handler.RegularPolygon.irregular" id=link1852 onMouseOver="ShowTip(event, 'tt1852', 'link1852')" onMouseOut="HideTip('tt1852')" class=IParent>OpenLayers.<wbr>Handler.<wbr>RegularPolygon</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Util-js.html#Util.IS_GECKO" id=link1853 onMouseOver="ShowTip(event, 'tt1853', 'link1853')" onMouseOut="HideTip('tt1853')" class=ISymbol>IS_GECKO</a>, <span class=IParent>Util</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>isAlphaImage</span><div class=ISubIndex><a href="../files/OpenLayers/Popup/Framed-js.html#OpenLayers.Popup.Framed.isAlphaImage" id=link1854 onMouseOver="ShowTip(event, 'tt1854', 'link1854')" onMouseOut="HideTip('tt1854')" class=IParent>OpenLayers.<wbr>Popup.<wbr>Framed</a><a href="../files/OpenLayers/Popup/FramedCloud-js.html#OpenLayers.Popup.FramedCloud.isAlphaImage" id=link1855 onMouseOver="ShowTip(event, 'tt1855', 'link1855')" onMouseOut="HideTip('tt1855')" class=IParent>OpenLayers.<wbr>Popup.<wbr>FramedCloud</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Util-js.html#Util.isArray" id=link1856 onMouseOver="ShowTip(event, 'tt1856', 'link1856')" onMouseOut="HideTip('tt1856')" class=ISymbol>isArray</a>, <span class=IParent>Util</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>isBaseLayer</span><div class=ISubIndex><a href="../files/OpenLayers/Layer-js.html#OpenLayers.Layer.isBaseLayer" id=link1857 onMouseOver="ShowTip(event, 'tt1857', 'link1857')" onMouseOut="HideTip('tt1857')" class=IParent>OpenLayers.<wbr>Layer</a><a href="../files/OpenLayers/Layer/ArcGIS93Rest-js.html#OpenLayers.Layer.ArcGIS93Rest.isBaseLayer" id=link1858 onMouseOver="ShowTip(event, 'tt1858', 'link1858')" onMouseOut="HideTip('tt1858')" class=IParent>OpenLayers.<wbr>Layer.<wbr>ArcGIS93Rest</a><a href="../files/OpenLayers/Layer/ArcIMS-js.html#OpenLayers.Layer.ArcIMS.isBaseLayer" id=link1859 onMouseOver="ShowTip(event, 'tt1859', 'link1859')" onMouseOut="HideTip('tt1859')" class=IParent>OpenLayers.<wbr>Layer.<wbr>ArcIMS</a><a href="../files/OpenLayers/Layer/EventPane-js.html#OpenLayers.Layer.EventPane.isBaseLayer" id=link1860 onMouseOver="ShowTip(event, 'tt1860', 'link1860')" onMouseOut="HideTip('tt1860')" class=IParent>OpenLayers.<wbr>Layer.<wbr>EventPane</a><a href="../files/OpenLayers/Layer/Image-js.html#OpenLayers.Layer.Image.isBaseLayer" id=link1861 onMouseOver="ShowTip(event, 'tt1861', 'link1861')" onMouseOut="HideTip('tt1861')" class=IParent>OpenLayers.<wbr>Layer.<wbr>Image</a><a href="../files/OpenLayers/Layer/KaMap-js.html#OpenLayers.Layer.KaMap.isBaseLayer" id=link1862 onMouseOver="ShowTip(event, 'tt1862', 'link1862')" onMouseOut="HideTip('tt1862')" class=IParent>OpenLayers.<wbr>Layer.<wbr>KaMap</a><a href="../files/OpenLayers/Layer/MapGuide-js.html#OpenLayers.Layer.MapGuide.isBaseLayer" id=link1863 onMouseOver="ShowTip(event, 'tt1863', 'link1863')" onMouseOut="HideTip('tt1863')" class=IParent>OpenLayers.<wbr>Layer.<wbr>MapGuide</a><a href="../files/OpenLayers/Layer/Markers-js.html#OpenLayers.Layer.Markers.isBaseLayer" id=link1864 onMouseOver="ShowTip(event, 'tt1864', 'link1864')" onMouseOut="HideTip('tt1864')" class=IParent>OpenLayers.<wbr>Layer.<wbr>Markers</a><a href="../files/OpenLayers/Layer/TileCache-js.html#OpenLayers.Layer.TileCache.isBaseLayer" id=link1865 onMouseOver="ShowTip(event, 'tt1865', 'link1865')" onMouseOut="HideTip('tt1865')" class=IParent>OpenLayers.<wbr>Layer.<wbr>TileCache</a><a href="../files/OpenLayers/Layer/TMS-js.html#OpenLayers.Layer.TMS.isBaseLayer" id=link1866 onMouseOver="ShowTip(event, 'tt1866', 'link1866')" onMouseOut="HideTip('tt1866')" class=IParent>OpenLayers.<wbr>Layer.TMS</a><a href="../files/OpenLayers/Layer/UTFGrid-js.html#OpenLayers.Layer.UTFGrid.isBaseLayer" id=link1867 onMouseOver="ShowTip(event, 'tt1867', 'link1867')" onMouseOut="HideTip('tt1867')" class=IParent>OpenLayers.<wbr>Layer.<wbr>UTFGrid</a><a href="../files/OpenLayers/Layer/Vector-js.html#OpenLayers.Layer.Vector.isBaseLayer" id=link1868 onMouseOver="ShowTip(event, 'tt1868', 'link1868')" onMouseOut="HideTip('tt1868')" class=IParent>OpenLayers.<wbr>Layer.<wbr>Vector</a><a href="../files/deprecated-js.html#OpenLayers.Layer.WFS.isBaseLayer" id=link1869 onMouseOver="ShowTip(event, 'tt1869', 'link1869')" onMouseOut="HideTip('tt1869')" class=IParent>OpenLayers.<wbr>Layer.WFS</a><a href="../files/OpenLayers/Layer/WMS-js.html#OpenLayers.Layer.WMS.isBaseLayer" id=link1870 onMouseOver="ShowTip(event, 'tt1870', 'link1870')" onMouseOut="HideTip('tt1870')" class=IParent>OpenLayers.<wbr>Layer.WMS</a><a href="../files/OpenLayers/Layer/WMTS-js.html#OpenLayers.Layer.WMTS.isBaseLayer" id=link1871 onMouseOver="ShowTip(event, 'tt1871', 'link1871')" onMouseOut="HideTip('tt1871')" class=IParent>OpenLayers.<wbr>Layer.WMTS</a><a href="../files/OpenLayers/Layer/WorldWind-js.html#OpenLayers.Layer.WorldWind.isBaseLayer" id=link1872 onMouseOver="ShowTip(event, 'tt1872', 'link1872')" onMouseOut="HideTip('tt1872')" class=IParent>OpenLayers.<wbr>Layer.<wbr>WorldWind</a><a href="../files/OpenLayers/Layer/XYZ-js.html#OpenLayers.Layer.XYZ.isBaseLayer" id=link1873 onMouseOver="ShowTip(event, 'tt1873', 'link1873')" onMouseOut="HideTip('tt1873')" class=IParent>OpenLayers.<wbr>Layer.XYZ</a><a href="../files/OpenLayers/Layer/Zoomify-js.html#OpenLayers.Layer.Zoomify.isBaseLayer" id=link1874 onMouseOver="ShowTip(event, 'tt1874', 'link1874')" onMouseOut="HideTip('tt1874')" class=IParent>OpenLayers.<wbr>Layer.<wbr>Zoomify</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Renderer/Elements-js.html#OpenLayers.Renderer.Elements.isComplexSymbol" id=link1875 onMouseOver="ShowTip(event, 'tt1875', 'link1875')" onMouseOut="HideTip('tt1875')" class=ISymbol>isComplexSymbol</a>, <span class=IParent>OpenLayers.<wbr>Renderer.<wbr>Elements</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>isDefault</span><div class=ISubIndex><a href="../files/OpenLayers/Style-js.html#OpenLayers.Style.isDefault" id=link1876 onMouseOver="ShowTip(event, 'tt1876', 'link1876')" onMouseOut="HideTip('tt1876')" class=IParent>OpenLayers.<wbr>Style</a><a href="../files/OpenLayers/Style2-js.html#OpenLayers.Style2.isDefault" id=link1877 onMouseOver="ShowTip(event, 'tt1877', 'link1877')" onMouseOut="HideTip('tt1877')" class=IParent>OpenLayers.<wbr>Style2</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>isDrawn</span><div class=ISubIndex><a href="../files/OpenLayers/Icon-js.html#OpenLayers.Icon.isDrawn" id=link1878 onMouseOver="ShowTip(event, 'tt1878', 'link1878')" onMouseOut="HideTip('tt1878')" class=IParent>OpenLayers.Icon</a><a href="../files/OpenLayers/Marker-js.html#OpenLayers.Marker.isDrawn" id=link1879 onMouseOver="ShowTip(event, 'tt1879', 'link1879')" onMouseOut="HideTip('tt1879')" class=IParent>OpenLayers.<wbr>Marker</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Util-js.html#Util.isElement" id=link1880 onMouseOver="ShowTip(event, 'tt1880', 'link1880')" onMouseOut="HideTip('tt1880')" class=ISymbol>isElement</a>, <span class=IParent>Util</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Control/Split-js.html#OpenLayers.Control.Split.isEligible" id=link1881 onMouseOver="ShowTip(event, 'tt1881', 'link1881')" onMouseOut="HideTip('tt1881')" class=ISymbol>isEligible</a>, <span class=IParent>OpenLayers.<wbr>Control.<wbr>Split</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Util-js.html#Util.isEquivalentUrl" id=link1882 onMouseOver="ShowTip(event, 'tt1882', 'link1882')" onMouseOut="HideTip('tt1882')" class=ISymbol>isEquivalentUrl</a>, <span class=IParent>Util</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Format/ArcXML-js.html#OpenLayers.Format.ArcXML.iserror" id=link1883 onMouseOver="ShowTip(event, 'tt1883', 'link1883')" onMouseOut="HideTip('tt1883')" class=ISymbol>iserror</a>, <span class=IParent>OpenLayers.<wbr>Format.<wbr>ArcXML</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><span class=ISymbol>isFixed</span><div class=ISubIndex><a href="../files/OpenLayers/Layer/EventPane-js.html#OpenLayers.Layer.EventPane.isFixed" id=link1884 onMouseOver="ShowTip(event, 'tt1884', 'link1884')" onMouseOut="HideTip('tt1884')" class=IParent>OpenLayers.<wbr>Layer.<wbr>EventPane</a><a href="../files/OpenLayers/Layer/Vector-js.html#OpenLayers.Layer.Vector.isFixed" id=link1885 onMouseOver="ShowTip(event, 'tt1885', 'link1885')" onMouseOut="HideTip('tt1885')" class=IParent>OpenLayers.<wbr>Layer.<wbr>Vector</a></div></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Events-js.html#OpenLayers.Event.isLeftClick" id=link1886 onMouseOver="ShowTip(event, 'tt1886', 'link1886')" onMouseOut="HideTip('tt1886')" class=ISymbol>isLeftClick</a>, <span class=IParent>OpenLayers.<wbr>Event</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Tile-js.html#OpenLayers.Tile.isLoading" id=link1887 onMouseOver="ShowTip(event, 'tt1887', 'link1887')" onMouseOut="HideTip('tt1887')" class=ISymbol>isLoading</a>, <span class=IParent>OpenLayers.Tile</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Events-js.html#OpenLayers.Event.isMultiTouch" id=link1888 onMouseOver="ShowTip(event, 'tt1888', 'link1888')" onMouseOut="HideTip('tt1888')" class=ISymbol>isMultiTouch</a>, <span class=IParent>OpenLayers.<wbr>Event</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Animation-js.html#OpenLayers.Animation.isNative" id=link1889 onMouseOver="ShowTip(event, 'tt1889', 'link1889')" onMouseOut="HideTip('tt1889')" class=ISymbol>isNative</a>, <span class=IParent>OpenLayers.<wbr>Animation</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/BaseTypes-js.html#OpenLayers.String.isNumeric" id=link1890 onMouseOver="ShowTip(event, 'tt1890', 'link1890')" onMouseOut="HideTip('tt1890')" class=ISymbol>isNumeric</a>, <span class=IParent>OpenLayers.<wbr>String</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/deprecated-js.html#OpenLayers.Class.isPrototype" id=link1891 onMouseOver="ShowTip(event, 'tt1891', 'link1891')" onMouseOut="HideTip('tt1891')" class=ISymbol>isPrototype</a>, <span class=IParent>OpenLayers.<wbr>Class</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Events-js.html#OpenLayers.Event.isRightClick" id=link1892 onMouseOver="ShowTip(event, 'tt1892', 'link1892')" onMouseOut="HideTip('tt1892')" class=ISymbol>isRightClick</a>, <span class=IParent>OpenLayers.<wbr>Event</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Format/XML-js.html#OpenLayers.Format.XML.isSimpleContent" id=link1893 onMouseOver="ShowTip(event, 'tt1893', 'link1893')" onMouseOut="HideTip('tt1893')" class=ISymbol>isSimpleContent</a>, <span class=IParent>OpenLayers.<wbr>Format.XML</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Events-js.html#OpenLayers.Event.isSingleTouch" id=link1894 onMouseOver="ShowTip(event, 'tt1894', 'link1894')" onMouseOut="HideTip('tt1894')" class=ISymbol>isSingleTouch</a>, <span class=IParent>OpenLayers.<wbr>Event</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Request-js.html#OpenLayers.Request.issue" id=link1895 onMouseOver="ShowTip(event, 'tt1895', 'link1895')" onMouseOut="HideTip('tt1895')" class=ISymbol>issue</a>, <span class=IParent>OpenLayers.<wbr>Request</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Control/OverviewMap-js.html#OpenLayers.Control.OverviewMap.isSuitableOverview" id=link1896 onMouseOver="ShowTip(event, 'tt1896', 'link1896')" onMouseOut="HideTip('tt1896')" class=ISymbol>isSuitableOverview</a>, <span class=IParent>OpenLayers.<wbr>Control.<wbr>OverviewMap</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Map-js.html#OpenLayers.Map.isValidLonLat" id=link1897 onMouseOver="ShowTip(event, 'tt1897', 'link1897')" onMouseOut="HideTip('tt1897')" class=ISymbol>isValidLonLat</a>, <span class=IParent>OpenLayers.Map</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Format/GeoJSON-js.html#OpenLayers.Format.GeoJSON.isValidType" id=link1898 onMouseOver="ShowTip(event, 'tt1898', 'link1898')" onMouseOut="HideTip('tt1898')" class=ISymbol>isValidType</a>, <span class=IParent>OpenLayers.<wbr>Format.<wbr>GeoJSON</span></td></tr><tr><td class=ISymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Map-js.html#OpenLayers.Map.isValidZoomLevel" id=link1899 onMouseOver="ShowTip(event, 'tt1899', 'link1899')" onMouseOut="HideTip('tt1899')" class=ISymbol>isValidZoomLevel</a>, <span class=IParent>OpenLayers.Map</span></td></tr><tr><td class=ISymbolPrefix id=ILastSymbolPrefix>&nbsp;</td><td class=IEntry><a href="../files/OpenLayers/Format/OSM-js.html#OpenLayers.Format.OSM.isWayArea" id=link1900 onMouseOver="ShowTip(event, 'tt1900', 'link1900')" onMouseOut="HideTip('tt1900')" class=ISymbol>isWayArea</a>, <span class=IParent>OpenLayers.<wbr>Format.OSM</span></td></tr></table>
<!--START_ND_TOOLTIPS-->
<div class=CToolTip id="tt1755"><div class=CFunction>Alias for OpenLayers.Lang.translate. </div></div><div class=CToolTip id="tt1756"><div class=CProperty>{OpenLayers.Icon}. </div></div><div class=CToolTip id="tt1757"><div class=CProperty>{OpenLayers.Icon} The icon used by this marker.</div></div><div class=CToolTip id="tt1758"><div class=CConstructor>Creates an icon, which is an image tag in a div.</div></div><div class=CToolTip id="tt1759"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">iconOff: function()</td></tr></table></blockquote>Internal use, for use only with &ldquo;controls[i].events.on/un&rdquo;.</div></div><div class=CToolTip id="tt1760"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">iconOn: function()</td></tr></table></blockquote>Internal use, for use only with &ldquo;controls[i].events.on/un&rdquo;.</div></div><div class=CToolTip id="tt1761"><div class=CProperty>{String}</div></div><div class=CToolTip id="tt1762"><div class=CProperty>{String}</div></div><div class=CToolTip id="tt1763"><div class=CProperty>{String} A unique identifier for this geometry.</div></div><div class=CToolTip id="tt1764"><div class=CProperty>{String}</div></div><div class=CToolTip id="tt1765"><div class=CProperty>{String}</div></div><div class=CToolTip id="tt1766"><div class=CProperty>{String} Unique identifier for the map</div></div><div class=CToolTip id="tt1767"><div class=CProperty>{String} the unique identifier assigned to this popup.</div></div><div class=CToolTip id="tt1768"><div class=CProperty>{String} A unique id for this session.</div></div><div class=CToolTip id="tt1769"><div class=CProperty>{String} A unique id for this session.</div></div><div class=CToolTip id="tt1770"><div class=CProperty>{String} A unique id for this session.</div></div><div class=CToolTip id="tt1771"><div class=CProperty>{String} null</div></div><div class=CToolTip id="tt1772"><div class=CProperty>{String} Process identifier known to the server.</div></div><div class=CToolTip id="tt1773"><div class=CConstant>Mixin for tiles that use form-encoded POST requests to get images from remote services. </div></div><div class=CToolTip id="tt1774"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>ignore: function(</td><td class="PParameter  prettyprint " nowrap>element</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Check for event target elements that should be ignored by OpenLayers.</div></div><div class=CToolTip id="tt1775"><div class=CProperty>{Boolean} Ignore dimensions higher than 2 when reading geometry coordinates.</div></div><div class=CToolTip id="tt1776"><div class=CConstructor>Create a new image layer</div></div><div class=CToolTip id="tt1777"><div class=CConstructor>Constructor for a new OpenLayers.Tile.Image instance.</div></div><div class=CToolTip id="tt1778"><div class=CConstant>{HTMLImageElement} The image for a tile.</div></div><div class=CToolTip id="tt1779"><div class=CConstant>{Object} Simple hash map to convert format to extension.</div></div><div class=CToolTip id="tt1780"><div class=CProperty>{Integer} How many times should we try to reload an image before giving up?&nbsp; </div></div><div class=CToolTip id="tt1781"><div class=CProperty>{DOMElement}</div></div><div class=CToolTip id="tt1782"><div class=CProperty>{String} The image format used for caching. </div></div><div class=CToolTip id="tt1783"><div class=CProperty>{Integer} Attempts to load the image.</div></div><div class=CToolTip id="tt1784"><div class=CProperty>{OpenLayers.Size} Size (measured in pixels) of the image located by the &lsquo;imageSrc&rsquo; property.</div></div><div class=CToolTip id="tt1785"><div class=CProperty>{OpenLayers.Size}</div></div><div class=CToolTip id="tt1786"><div class=CProperty>{String} location of the image to be used as the popup frame</div></div><div class=CToolTip id="tt1787"><div class=CProperty>{HTMLImageElement} The image for this tile.</div></div><div class=CToolTip id="tt1788"><div class=CProperty>{String} Set this to the path where control images are stored, a path given here must end with a slash. </div></div><div class=CToolTip id="tt1789"><div class=CProperty>{String} Set this to the path where control images are stored, a path given here must end with a slash. </div></div><div class=CToolTip id="tt1790"><div class=CProperty>{Boolean} Activates the immediate measurement so that the &ldquo;measurepartial&rdquo; event is also fired once the measurement sketch is modified. </div></div><div class=CToolTip id="tt1791"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>importSymbol: function (</td><td class="PParameter  prettyprint " nowrap>graphicName</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>add a new symbol definition from the rendererer&rsquo;s symbol hash</div></div><div class=CToolTip id="tt1792"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>importSymbol: function (</td><td class="PParameter  prettyprint " nowrap>graphicName</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>add a new symbol definition from the rendererer&rsquo;s symbol hash</div></div><div class=CToolTip id="tt1793"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>importSymbol: function (</td><td class="PParameter  prettyprint " nowrap>graphicName</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>add a new symbol definition from the rendererer&rsquo;s symbol hash</div></div><div class=CToolTip id="tt1794"><div class=CConstant>{Object} Constant inches per unit -- borrowed from MapServer mapscale.c derivation of nautical miles from http://en.wikipedia.org/wiki/Nautical_mile Includes the full set of units supported by CS-MAP (http://trac.osgeo.org/csmap/) and PROJ.4 (http://trac.osgeo.org/proj/) The hardcoded table is maintain in a CS-MAP source code module named CSdataU.c The hardcoded table of PROJ.4 units are in pj_units.c.</div></div><div class=CToolTip id="tt1795"><div class=CProperty>{Boolean} Should the .xy property automatically be created for browser mouse events?&nbsp; </div></div><div class=CToolTip id="tt1796"><div class=CProperty>{String} For &ldquo;pretty&rdquo; printing, the indent string will be used once for each indentation level.</div></div><div class=CToolTip id="tt1797"><div class=CProperty>{OpenLayers.ElementIndexer} An instance of OpenLayers.ElementsIndexer created upon initialization if the zIndexing or yOrdering options passed to this renderer&rsquo;s constructor are set to true.</div></div><div class=CToolTip id="tt1798"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>indexFromCharCode: function(</td><td class="PParameter  prettyprint " nowrap>charCode</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Given a character code for one of the UTFGrid &ldquo;grid&rdquo; characters, resolve the integer index for the feature id in the UTFGrid &ldquo;keys&rdquo; array.</div></div><div class=CToolTip id="tt1799"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>OpenLayers.Util.indexOf = function(</td><td class="PParameter  prettyprint " nowrap>array,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>obj</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Seems to exist already in FF, but not in MOZ.</div></div><div class=CToolTip id="tt1800"><div class=CProperty>{Object} This is a hash that maps node ids to their z-index value stored in the indexer. </div></div><div class=CToolTip id="tt1801"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>inflate: function(</td><td class="PParameter  prettyprint " nowrap>inflate</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Englarges the markers icon by the specified ratio.</div></div><div class=CToolTip id="tt1802"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">info: function()</td></tr></table></blockquote>Writes a message to the console with the visual &ldquo;info&rdquo; icon and color coding and a hyperlink to the line where it was called.</div></div><div class=CToolTip id="tt1803"><div class=CProperty>{String} The mimetype to request from the server. </div></div><div class=CToolTip id="tt1804"><div class=CProperty>{String} The mimetype to request from the server</div></div><div class=CToolTip id="tt1805"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>OpenLayers.inherit = function(</td><td class="PParameter  prettyprint " nowrap>C,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>P</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote></div></div><div class=CToolTip id="tt1806"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>OpenLayers.Class.inherit = function (</td><td class="PParameter  prettyprint " nowrap>P</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote><b>Deprecated</b>. </div></div><div class=CToolTip id="tt1807"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">initGmlParser: function()</td></tr></table></blockquote>Creates a GML parser.</div></div><div class=CToolTip id="tt1808"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>initGriddedTiles: function(</td><td class="PParameter  prettyprint " nowrap>bounds</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote></div></div><div class=CToolTip id="tt1809"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>initGriddedTiles:function(</td><td class="PParameter  prettyprint " nowrap>bounds</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote></div></div><div class=CToolTip id="tt1810"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>initialize: function(</td><td class="PParameter  prettyprint " nowrap>yOrdering</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Create a new indexer with</div></div><div class=CToolTip id="tt1811"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">initializeDatabase: function()</td></tr></table></blockquote></div></div><div class=CToolTip id="tt1812"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>initializeZoomify: function(</td><td class="PParameter  prettyprint " nowrap>size</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>It generates constants for all tiers of the Zoomify pyramid</div></div><div class=CToolTip id="tt1813"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">initImage: function()</td></tr></table></blockquote>Creates the content for the frame on the tile.</div></div><div class=CToolTip id="tt1814"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>initLayer: function(</td><td class="PParameter  prettyprint " nowrap>layers</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Assign the layer property. </div></div><div class=CToolTip id="tt1815"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">initLayer: function()</td></tr></table></blockquote>Sets layer properties according to the metadata provided by the API</div></div><div class=CToolTip id="tt1816"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">initMercatorParameters: function()</td></tr></table></blockquote>Set up the mercator parameters on the layer: resolutions, projection, units.</div></div><div class=CToolTip id="tt1817"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">initProperties: function()</td></tr></table></blockquote>Set any properties that depend on the value of singleTile. </div></div><div class=CToolTip id="tt1818"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">initResolutions: function()</td></tr></table></blockquote>This method&rsquo;s responsibility is to set up the &lsquo;resolutions&rsquo; array for the layer -- this array is what the layer will use to interface between the zoom levels of the map and the resolution display of the layer.</div></div><div class=CToolTip id="tt1819"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">initResolutions: function()</td></tr></table></blockquote>Populate the resolutions array</div></div><div class=CToolTip id="tt1820"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>initSingleTile: function(</td><td class="PParameter  prettyprint " nowrap>bounds</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote></div></div><div class=CToolTip id="tt1821"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">initStack: function()</td></tr></table></blockquote>Called after the control is activated if the previous history stack is empty.</div></div><div class=CToolTip id="tt1822"><div class=CProperty>{Boolean} The current map resolution is within the layer&rsquo;s min/max range. </div></div><div class=CToolTip id="tt1823"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>insert: function(</td><td class="PParameter  prettyprint " nowrap>newNode</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Insert a new node into the indexer. </div></div><div class=CToolTip id="tt1824"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>insert: function(</td><td class="PParameter  prettyprint " nowrap>feature</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Takes a feature, and generates a WFS-T Transaction &ldquo;Insert&rdquo;</div></div><div class=CToolTip id="tt1825"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>insertDeflectionLength: function(</td><td class="PParameter  prettyprint " nowrap>deflection,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>length</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Insert a point in the current sketch given a deflection and a length. </div></div><div class=CToolTip id="tt1826"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>insertDeflectionLength: function(</td><td class="PParameter  prettyprint " nowrap>deflection,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>length</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Insert a point in the current sketch given a deflection and a length. </div></div><div class=CToolTip id="tt1827"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>insertDeltaXY: function(</td><td class="PParameter  prettyprint " nowrap>dx,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>dy</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Insert a point given offsets from the previously inserted point.</div></div><div class=CToolTip id="tt1828"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>insertDeltaXY: function(</td><td class="PParameter  prettyprint " nowrap>dx,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>dy</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Insert a point given offsets from the previously inserted point.</div></div><div class=CToolTip id="tt1829"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>insertDirectionLength: function(</td><td class="PParameter  prettyprint " nowrap>direction,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>length</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Insert a point in the current sketch given a direction and a length.</div></div><div class=CToolTip id="tt1830"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>insertDirectionLength: function(</td><td class="PParameter  prettyprint " nowrap>direction,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>length</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Insert a point in the current sketch given a direction and a length.</div></div><div class=CToolTip id="tt1831"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>insertXY: function(</td><td class="PParameter  prettyprint " nowrap>x,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>y</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Insert a point in the current sketch given x &amp; y coordinates.</div></div><div class=CToolTip id="tt1832"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>insertXY: function(</td><td class="PParameter  prettyprint " nowrap>x,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>y</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Insert a point in the current sketch given x &amp; y coordinates. </div></div><div class=CToolTip id="tt1833"><div class=CProperty>{Array} List of tags to exclude from &lsquo;interesting&rsquo; checks on nodes. </div></div><div class=CToolTip id="tt1834"><div class=CProperty>{String} KML Namespace to use -- defaults to the namespace of the Placemark node being parsed, but falls back to kmlns.</div></div><div class=CToolTip id="tt1835"><div class=CProperty>{OpenLayers.Projection} When passed a externalProjection and internalProjection, the format will reproject the geometries it reads or writes. </div></div><div class=CToolTip id="tt1836"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>intersects: function(</td><td class="PParameter  prettyprint " nowrap>geometry</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Determine if the input geometry intersects this one.</div></div><div class=CToolTip id="tt1837"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>intersects: function(</td><td class="PParameter  prettyprint " nowrap>geometry</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Determine if the input geometry intersects this one.</div></div><div class=CToolTip id="tt1838"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>intersects: function(</td><td class="PParameter  prettyprint " nowrap>geometry</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Test for instersection between two geometries. </div></div><div class=CToolTip id="tt1839"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>intersects: function(</td><td class="PParameter  prettyprint " nowrap>geometry</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Determine if the input geometry intersects this one.</div></div><div class=CToolTip id="tt1840"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>intersects: function(</td><td class="PParameter  prettyprint " nowrap>geometry</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Determine if the input geometry intersects this one.</div></div><div class=CToolTip id="tt1841"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>intersectsBounds:function(</td><td class="PParameter  prettyprint " nowrap>bounds,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>options</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Determine whether the target bounds intersects this bounds. </div></div><div class=CToolTip id="tt1842"><div class=CProperty>{Integer} The number of milliseconds that should ellapse before panning the map again. </div></div><div class=CToolTip id="tt1843"><div class=CProperty>{Integer} In order to increase performance, an interval (in milliseconds) can be set to reduce the number of drag events called. </div></div><div class=CToolTip id="tt1844"><div class=CProperty>{Integer} In order to increase server performance, an interval (in milliseconds) can be set to reduce the number of up/down events called. </div></div><div class=CToolTip id="tt1845"><div class=CProperty>{Number} Auto-refresh. </div></div><div class=CToolTip id="tt1846"><div class=CProperty>{Array(Float)} A list of possible graticule widths in degrees.</div></div><div class=CToolTip id="tt1847"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">invalidBounds: function()</td></tr></table></blockquote>Determine whether the previously generated point grid is invalid. </div></div><div class=CToolTip id="tt1848"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>invalidBounds: function(</td><td class="PParameter  prettyprint " nowrap>mapBounds</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Determine whether the previously requested set of features is invalid. </div></div><div class=CToolTip id="tt1849"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>inValidRange: function(</td><td class="PParameter  prettyprint " nowrap>x,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>y,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>xyOnly</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>See #669 for more information</div></div><div class=CToolTip id="tt1850"><div class=CFunction>Given a x,y in Spherical Mercator, return a point in EPSG:4326.</div></div><div class=CToolTip id="tt1851"><div class=CProperty>{Boolean} Make scaling/resizing work irregularly. </div></div><div class=CToolTip id="tt1852"><div class=CProperty>{Boolean} Draw an irregular polygon instead of a regular polygon. </div></div><div class=CToolTip id="tt1853"><div class=CConstant>{Boolean} True if the userAgent reports the browser to use the Gecko engine</div></div><div class=CToolTip id="tt1854"><div class=CProperty>{Boolean} The image has some alpha and thus needs to use the alpha image hack. </div></div><div class=CToolTip id="tt1855"><div class=CProperty>{Boolean} The FramedCloud does not use an alpha image (in honor of the good ie6 folk out there)</div></div><div class=CToolTip id="tt1856"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>OpenLayers.Util.isArray = function(</td><td class="PParameter  prettyprint " nowrap>a</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Tests that the provided object is an array. </div></div><div class=CToolTip id="tt1857"><div class=CProperty>{Boolean} Whether or not the layer is a base layer. </div></div><div class=CToolTip id="tt1858"><div class=CProperty>{Boolean} Default is true for ArcGIS93Rest layer</div></div><div class=CToolTip id="tt1859"><div class=CProperty>{Boolean} The layer is a base layer. </div></div><div class=CToolTip id="tt1860"><div class=CProperty>{Boolean} EventPaned layers are always base layers, by necessity.</div></div><div class=CToolTip id="tt1861"><div class=CProperty>{Boolean} The layer is a base layer. </div></div><div class=CToolTip id="tt1862"><div class=CProperty>{Boolean} KaMap Layer is always a base layer</div></div><div class=CToolTip id="tt1863"><div class=CProperty>{Boolean} Treat this layer as a base layer. </div></div><div class=CToolTip id="tt1864"><div class=CProperty>{Boolean} Markers layer is never a base layer.</div></div><div class=CToolTip id="tt1865"><div class=CProperty>{Boolean} Treat this layer as a base layer. </div></div><div class=CToolTip id="tt1866"><div class=CProperty>{Boolean} Make this layer a base layer. </div></div><div class=CToolTip id="tt1867"><div class=CProperty>Default is false, as UTFGrids are designed to be a transparent overlay layer.</div></div><div class=CToolTip id="tt1868"><div class=CProperty>{Boolean} The layer is a base layer. </div></div><div class=CToolTip id="tt1869"><div class=CProperty>{Boolean} WFS layer is not a base layer by default.</div></div><div class=CToolTip id="tt1870"><div class=CProperty>{Boolean} Default is true for WMS layer</div></div><div class=CToolTip id="tt1871"><div class=CProperty>{Boolean} The layer will be considered a base layer. </div></div><div class=CToolTip id="tt1872"><div class=CProperty>{Boolean} WorldWind layer is a base layer by default.</div></div><div class=CToolTip id="tt1873"><div class=CProperty>Default is true, as this is designed to be a base tile source.</div></div><div class=CToolTip id="tt1874"><div class=CProperty>{Boolean}</div></div><div class=CToolTip id="tt1875"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>isComplexSymbol: function(</td><td class="PParameter  prettyprint " nowrap>graphicName</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Determines if a symbol cannot be rendered using drawCircle</div></div><div class=CToolTip id="tt1876"><div class=CProperty>{Boolean}</div></div><div class=CToolTip id="tt1877"><div class=CProperty>{Boolean}</div></div><div class=CToolTip id="tt1878"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">isDrawn: function()</td></tr></table></blockquote>{Boolean} Whether or not the icon is drawn.</div></div><div class=CToolTip id="tt1879"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">isDrawn: function()</td></tr></table></blockquote>{Boolean} Whether or not the marker is drawn.</div></div><div class=CToolTip id="tt1880"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>OpenLayers.Util.isElement = function(</td><td class="PParameter  prettyprint " nowrap>o</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>A cross-browser implementation of &ldquo;e instanceof Element&rdquo;.</div></div><div class=CToolTip id="tt1881"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>isEligible: function(</td><td class="PParameter  prettyprint " nowrap>target</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Test if a target feature is eligible for splitting.</div></div><div class=CToolTip id="tt1882"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>OpenLayers.Util.isEquivalentUrl = function(</td><td class="PParameter  prettyprint " nowrap>url1,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>url2,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>options</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Test two URLs for equivalence.</div></div><div class=CToolTip id="tt1883"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>iserror: function(</td><td class="PParameter  prettyprint " nowrap>data</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Check to see if the response from the server was an error.</div></div><div class=CToolTip id="tt1884"><div class=CProperty>{Boolean} EventPaned layers are fixed by default.</div></div><div class=CToolTip id="tt1885"><div class=CProperty>{Boolean} Whether the layer remains in one place while dragging the map. </div></div><div class=CToolTip id="tt1886"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>isLeftClick: function(</td><td class="PParameter  prettyprint " nowrap>event</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Determine whether event was caused by a left click.</div></div><div class=CToolTip id="tt1887"><div class=CProperty>{Boolean} Is the tile loading?</div></div><div class=CToolTip id="tt1888"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>isMultiTouch: function(</td><td class="PParameter  prettyprint " nowrap>event</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Determine whether event was caused by a multi touch</div></div><div class=CToolTip id="tt1889"><div class=CProperty>{Boolean} true if a native requestAnimationFrame function is available</div></div><div class=CToolTip id="tt1890"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>isNumeric: function(</td><td class="PParameter  prettyprint " nowrap>value</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Determine whether a string contains only a numeric value.</div></div><div class=CToolTip id="tt1891"><div class=CProperty><b>Deprecated</b>. </div></div><div class=CToolTip id="tt1892"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>isRightClick: function(</td><td class="PParameter  prettyprint " nowrap>event</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Determine whether event was caused by a right mouse click.</div></div><div class=CToolTip id="tt1893"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>isSimpleContent: function(</td><td class="PParameter  prettyprint " nowrap>node</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Test if the given node has only simple content (i.e. </div></div><div class=CToolTip id="tt1894"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>isSingleTouch: function(</td><td class="PParameter  prettyprint " nowrap>event</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Determine whether event was caused by a single touch</div></div><div class=CToolTip id="tt1895"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>issue: function(</td><td class="PParameter  prettyprint " nowrap>config</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Create a new XMLHttpRequest object, open it, set any headers, bind a callback to done state, and send any data. </div></div><div class=CToolTip id="tt1896"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td class="prettyprint">isSuitableOverview: function()</td></tr></table></blockquote>Determines if the overview map is suitable given the extent and resolution of the main map.</div></div><div class=CToolTip id="tt1897"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>isValidLonLat: function(</td><td class="PParameter  prettyprint " nowrap>lonlat</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote></div></div><div class=CToolTip id="tt1898"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>isValidType: function(</td><td class="PParameter  prettyprint " nowrap>obj,</td></tr><tr><td></td><td class="PParameter  prettyprint " nowrap>type</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Check if a GeoJSON object is a valid representative of the given type.</div></div><div class=CToolTip id="tt1899"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>isValidZoomLevel: function(</td><td class="PParameter  prettyprint " nowrap>zoomLevel</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote></div></div><div class=CToolTip id="tt1900"><div class=CFunction><blockquote><table border=0 cellspacing=0 cellpadding=0 class="Prototype"><tr><td><table border=0 cellspacing=0 cellpadding=0><tr><td class="PBeforeParameters  prettyprint "nowrap>isWayArea: function(</td><td class="PParameter  prettyprint " nowrap>way</td><td class="PAfterParameters  prettyprint "nowrap>)</td></tr></table></td></tr></table></blockquote>Given a way object from getWays, check whether the tags and geometry indicate something is an area.</div></div><!--END_ND_TOOLTIPS-->

</div><!--Index-->


<div id=Footer><a href="http://www.naturaldocs.org">Generated by Natural Docs</a></div><!--Footer-->


<div id=Menu><div class=MTitle>OpenLayers<div class=MSubTitle>JavaScript Mapping Library</div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent35')">OpenLayers</a><div class=MGroupContent id=MGroupContent35><div class=MEntry><div class=MFile><a href="../files/OpenLayers-js.html">OpenLayers</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent1')">BaseTypes</a><div class=MGroupContent id=MGroupContent1><div class=MEntry><div class=MFile><a href="../files/OpenLayers/BaseTypes-js.html">Base Types</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/BaseTypes/Bounds-js.html">Bounds</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/BaseTypes/Class-js.html">Class</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/BaseTypes/Date-js.html">Date</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/BaseTypes/Element-js.html">Element</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/BaseTypes/LonLat-js.html">LonLat</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/BaseTypes/Pixel-js.html">Pixel</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/BaseTypes/Size-js.html">Size</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent3')">Control</a><div class=MGroupContent id=MGroupContent3><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control-js.html">Control</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent2')">Control</a><div class=MGroupContent id=MGroupContent2><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/ArgParser-js.html">ArgParser</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/Attribution-js.html">Attribution</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/Button-js.html">Button</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/CacheRead-js.html">CacheRead</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/CacheWrite-js.html">CacheWrite</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/DragFeature-js.html">DragFeature</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/DragPan-js.html">DragPan</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/DrawFeature-js.html">DrawFeature</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/EditingToolbar-js.html">EditingToolbar</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/Geolocate-js.html">Geolocate</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/GetFeature-js.html">GetFeature</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/Graticule-js.html">Graticule</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/KeyboardDefaults-js.html">KeyboardDefaults</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/LayerSwitcher-js.html">LayerSwitcher</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/Measure-js.html">Measure</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/ModifyFeature-js.html">ModifyFeature</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/MousePosition-js.html">MousePosition</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/Navigation-js.html">Navigation</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/NavigationHistory-js.html">NavigationHistory</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/NavToolbar-js.html">NavToolbar</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/OverviewMap-js.html">OverviewMap</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/Pan-js.html">Pan</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/Panel-js.html">Panel</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/PanPanel-js.html">PanPanel</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/PanZoom-js.html">PanZoom</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/PanZoomBar-js.html">PanZoomBar</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/Permalink-js.html">Permalink</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/PinchZoom-js.html">PinchZoom</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/Scale-js.html">Scale</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/ScaleLine-js.html">ScaleLine</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/SelectFeature-js.html">SelectFeature</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/SLDSelect-js.html">SLDSelect</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/Snapping-js.html">Snapping</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/Split-js.html">Split</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/TouchNavigation-js.html">TouchNavigation</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/TransformFeature-js.html">TransformFeature</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/UTFGrid-js.html">UTFGrid</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/WMSGetFeatureInfo-js.html">WMSGetFeatureInfo</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/WMTSGetFeatureInfo-js.html">WMTSGetFeatureInfo</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/Zoom-js.html">Zoom</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/ZoomBox-js.html">ZoomBox</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/ZoomIn-js.html">ZoomIn</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/ZoomOut-js.html">ZoomOut</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/ZoomPanel-js.html">ZoomPanel</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Control/ZoomToMaxExtent-js.html">ZoomToMaxExtent</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent4')">Feature</a><div class=MGroupContent id=MGroupContent4><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Feature-js.html">Feature</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Feature/Vector-js.html">Vector</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent5')">Filter</a><div class=MGroupContent id=MGroupContent5><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Filter-js.html">Filter</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Filter/Comparison-js.html">Comparison</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Filter/FeatureId-js.html">FeatureId</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Filter/Function-js.html">Function</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Filter/Logical-js.html">Logical</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Filter/Spatial-js.html">Spatial</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent17')">Format</a><div class=MGroupContent id=MGroupContent17><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format-js.html">Format</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent6')">Filter</a><div class=MGroupContent id=MGroupContent6><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/Filter-js.html">Filter</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/Filter/v1-js.html">v1</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/Filter/v1_0_0-js.html">v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/Filter/v1_1_0-js.html">v1_1_0</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent7')">GML</a><div class=MGroupContent id=MGroupContent7><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/GML-js.html">GML</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/GML/Base-js.html">Base</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/GML/v2-js.html">v2</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/GML/v3-js.html">v3</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent8')">SLD</a><div class=MGroupContent id=MGroupContent8><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/SLD-js.html">SLD</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/SLD/v1_0_0_GeoServer-js.html">SLD/<wbr>v1_0_0_GeoServer</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/SLD/v1-js.html">v1</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/SLD/v1_0_0-js.html">v1_0_0</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent9')">OWSCommon</a><div class=MGroupContent id=MGroupContent9><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/OWSCommon-js.html">OWSCommon</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/OWSCommon/v1-js.html">v1</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/OWSCommon/v1_0_0-js.html">v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/OWSCommon/v1_1_0-js.html">v1_1_0</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent10')">WFSCapabilities</a><div class=MGroupContent id=MGroupContent10><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WFSCapabilities-js.html">WFSCapabilities</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WFSCapabilities/v1-js.html">v1</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WFSCapabilities/v1_0_0-js.html">v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WFSCapabilities/v1_1_0-js.html">v1_1_0</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent11')">WFST</a><div class=MGroupContent id=MGroupContent11><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WFST-js.html">WFST</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WFST/v1-js.html">v1</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WFST/v1_0_0-js.html">v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WFST/v1_1_0-js.html">v1_1_0</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent12')">WMC</a><div class=MGroupContent id=MGroupContent12><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WMC-js.html">WMC</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WMC/v1-js.html">v1</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WMC/v1_0_0-js.html">v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WMC/v1_1_0-js.html">v1_1_0</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent13')">WMSCapabilities</a><div class=MGroupContent id=MGroupContent13><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WMSCapabilities-js.html">WMSCapabilities</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WMSCapabilities/v1-js.html">v1</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WMSCapabilities/v1_1-js.html">v1_1</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WMSCapabilities/v1_1_0-js.html">v1_1_0</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WMSCapabilities/v1_1_1-js.html">v1_1_1</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WMSCapabilities/v1_3-js.html">v1_3</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WMSCapabilities/v1_3_0-js.html">v1_3_0</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WMSCapabilities/v1_1_1_WMSC-js.html">WMSCapabilities/<wbr>v1_1_1_WMSC</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent14')">WMSDescribeLayer</a><div class=MGroupContent id=MGroupContent14><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WMSDescribeLayer-js.html">WMSDescribeLayer</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WMSDescribeLayer/v1_1-js.html">v1_1</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent16')">Format</a><div class=MGroupContent id=MGroupContent16><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/ArcXML-js.html">ArcXML</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/ArcXML/Features-js.html">ArcXML.<wbr>Features</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/Atom-js.html">Atom</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/Context-js.html">Context</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/CQL-js.html">CQL</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/CSWGetDomain-js.html">CSWGetDomain</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/CSWGetDomain/v2_0_2-js.html">CSWGetDomain.<wbr>v2_0_2</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/CSWGetRecords-js.html">CSWGetRecords</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/CSWGetRecords/v2_0_2-js.html">CSWGetRecords.<wbr>v2_0_2</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/EncodedPolyline-js.html">EncodedPolyline</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/GeoJSON-js.html">GeoJSON</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/GeoRSS-js.html">GeoRSS</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/GPX-js.html">GPX</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/JSON-js.html">JSON</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/KML-js.html">KML</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/OGCExceptionReport-js.html">OGCExceptionReport</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/OSM-js.html">OSM</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/OWSContext-js.html">OWSContext</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/OWSContext/v0_3_1-js.html">OWSContext.<wbr>v0_3_1</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/QueryStringFilter-js.html">QueryStringFilter</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/SOSCapabilities-js.html">SOSCapabilities</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/SOSCapabilities/v1_0_0-js.html">SOSCapabilities.<wbr>v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/SOSGetFeatureOfInterest-js.html">SOSGetFeatureOfInterest</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/SOSGetObservation-js.html">SOSGetObservation</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/Text-js.html">Text</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WCSCapabilities-js.html">WCSCapabilities</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent15')">WCSCapabilities</a><div class=MGroupContent id=MGroupContent15><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WCSCapabilities/v1-js.html">WCSCapabilities.v1</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WCSCapabilities/v1_0_0-js.html">WCSCapabilities/<wbr>v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WCSCapabilities/v1_1_0-js.html">WCSCapabilities/<wbr>v1_1_0</a></div></div></div></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WCSGetCoverage-js.html">WCSGetCoverage version 1.1.0</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WFS-js.html">WFS</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WFSDescribeFeatureType-js.html">WFSDescribeFeatureType</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WKT-js.html">WKT</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WMSGetFeatureInfo-js.html">WMSGetFeatureInfo</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WMTSCapabilities-js.html">WMTSCapabilities</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WMTSCapabilities/v1_0_0-js.html">WMTSCapabilities.<wbr>v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WPSCapabilities-js.html">WPSCapabilities</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WPSCapabilities/v1_0_0-js.html">WPSCapabilities.<wbr>v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WPSDescribeProcess-js.html">WPSDescribeProcess</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/WPSExecute-js.html">WPSExecute version 1.0.0</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/XLS-js.html">XLS</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/XLS/v1-js.html">XLS.v1</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/XLS/v1_1_0-js.html">XLS.<wbr>v1_1_0</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/XML-js.html">XML</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Format/XML/VersionedOGC-js.html">XML.<wbr>VersionedOGC</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent18')">Geometry</a><div class=MGroupContent id=MGroupContent18><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Geometry-js.html">Geometry</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Geometry/Collection-js.html">Collection</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Geometry/Curve-js.html">Curve</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Geometry/LinearRing-js.html">LinearRing</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Geometry/LineString-js.html">LineString</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Geometry/MultiLineString-js.html">MultiLineString</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Geometry/MultiPoint-js.html">MultiPoint</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Geometry/MultiPolygon-js.html">MultiPolygon</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Geometry/Point-js.html">Point</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Geometry/Polygon-js.html">Polygon</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent19')">Handler</a><div class=MGroupContent id=MGroupContent19><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Handler-js.html">Handler</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Handler/Box-js.html">Box</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Handler/Click-js.html">Click</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Handler/Drag-js.html">Drag</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Handler/Feature-js.html">Feature</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Handler/Hover-js.html">Hover</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Handler/Keyboard-js.html">Keyboard</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Handler/MouseWheel-js.html">MouseWheel</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Handler/Path-js.html">Path</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Handler/Pinch-js.html">Pinch</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Handler/Point-js.html">Point</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Handler/Polygon-js.html">Polygon</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Handler/RegularPolygon-js.html">RegularPolygon</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent21')">Lang</a><div class=MGroupContent id=MGroupContent21><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang-js.html">Lang</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent20')">Lang</a><div class=MGroupContent id=MGroupContent20><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/ar-js.html">ar</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/be-tarask-js.html">be-tarask</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/bg-js.html">bg</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/br-js.html">br</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/ca-js.html">ca</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/cs-CZ-js.html">cs-CZ</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/da-DK-js.html">da-DK</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/de-js.html">de</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/en-js.html">en</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/en-CA-js.html">en-CA</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/es-js.html">es</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/el-js.html">el</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/fi-js.html">fi</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/fr-js.html">fr</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/fur-js.html">fur</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/gl-js.html">gl</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/gsw-js.html">gsw</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/hr-js.html">hr</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/hsb-js.html">hsb</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/hu-js.html">hu</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/ia-js.html">ia</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/id-js.html">id</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/io-js.html">io</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/is-js.html">is</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/it-js.html">it</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/ja-js.html">ja</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/km-js.html">km</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/ksh-js.html">ksh</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/lt-js.html">lt</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/nds-js.html">nds</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/nb-js.html">nb</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/nl-js.html">nl</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/nn-js.html">nn</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/oc-js.html">oc</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/pl-js.html">pl</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/pt-js.html">pt</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/pt-BR-js.html">pt-BR</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/ru-js.html">ru</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/sk-js.html">sk</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/sv-SE-js.html">sv-SE</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/te-js.html">te</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/vi-js.html">vi</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/zh-CN-js.html">zh-CN</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/zh-TW-js.html">zh-TW</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Lang/ro-js.html">Lang[&ldquo;ro&rdquo;]</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent23')">Layer</a><div class=MGroupContent id=MGroupContent23><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer-js.html">Layer</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent22')">Layer</a><div class=MGroupContent id=MGroupContent22><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/ArcGISCache-js.html">ArcGISCache.js</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/ArcGIS93Rest-js.html">ArcGIS93Rest</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/ArcIMS-js.html">ArcIMS</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/Bing-js.html">Bing</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/Boxes-js.html">Boxes</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/EventPane-js.html">EventPane</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/FixedZoomLevels-js.html">FixedZoomLevels</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/GeoRSS-js.html">GeoRSS</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/Google-js.html">Google</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/Google/v3-js.html">Google.v3</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/Grid-js.html">Grid</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/HTTPRequest-js.html">HTTPRequest</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/Image-js.html">Image</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/KaMap-js.html">KaMap</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/KaMapCache-js.html">KaMapCache</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/MapGuide-js.html">MapGuide</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/MapServer-js.html">MapServer</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/Markers-js.html">Markers</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/PointGrid-js.html">PointGrid</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/PointTrack-js.html">PointTrack</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/SphericalMercator-js.html">SphericalMercator</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/Text-js.html">Text</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/TileCache-js.html">TileCache</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/TMS-js.html">TMS</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/Vector-js.html">Vector</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/Vector/RootContainer-js.html">Vector.<wbr>RootContainer</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/WMS-js.html">WMS</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/WMTS-js.html">WMTS</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/WorldWind-js.html">WorldWind</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/XYZ-js.html">XYZ</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/Zoomify-js.html">Zoomify</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/OSM-js.html">OSM</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Layer/UTFGrid-js.html">UTFGrid</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent24')">Marker</a><div class=MGroupContent id=MGroupContent24><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Marker-js.html">Marker</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Marker/Box-js.html">Box</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent25')">Popup</a><div class=MGroupContent id=MGroupContent25><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Popup-js.html">Popup</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Popup/Anchored-js.html">Anchored</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Popup/Framed-js.html">Framed</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Popup/FramedCloud-js.html">FramedCloud</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent28')">Protocol</a><div class=MGroupContent id=MGroupContent28><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Protocol-js.html">Protocol</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent26')">Protocol</a><div class=MGroupContent id=MGroupContent26><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Protocol/CSW-js.html">CSW</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Protocol/CSW/v2_0_2-js.html">CSW.<wbr>v2_0_2</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Protocol/HTTP-js.html">HTTP</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Protocol/Script-js.html">Script</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Protocol/SOS-js.html">SOS.<wbr>DEFAULTS</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Protocol/SOS/v1_0_0-js.html">SOS.<wbr>v1_0_0</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent27')">WFS</a><div class=MGroupContent id=MGroupContent27><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Protocol/WFS-js.html">WFS</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Protocol/WFS/v1-js.html">v1</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Protocol/WFS/v1_0_0-js.html">v1_0_0</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Protocol/WFS/v1_1_0-js.html">v1_1_0</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent29')">Renderer</a><div class=MGroupContent id=MGroupContent29><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Renderer-js.html">Renderer</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Renderer/Canvas-js.html">Canvas</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Renderer/Elements-js.html">ElementsIndexer</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Renderer/SVG-js.html">SVG</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Renderer/VML-js.html">VML</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent30')">Request</a><div class=MGroupContent id=MGroupContent30><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Request-js.html">Request</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Request/XMLHttpRequest-js.html">XMLHttpRequest</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent31')">Strategy</a><div class=MGroupContent id=MGroupContent31><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Strategy-js.html">Strategy</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Strategy/BBOX-js.html">BBOX</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Strategy/Cluster-js.html">Cluster</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Strategy/Filter-js.html">Filter</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Strategy/Fixed-js.html">Fixed</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Strategy/Paging-js.html">Paging</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Strategy/Refresh-js.html">Refresh</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Strategy/Save-js.html">Save</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent32')">Symbolizer</a><div class=MGroupContent id=MGroupContent32><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Symbolizer-js.html">Symbolizer</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Symbolizer/Line-js.html">Line</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Symbolizer/Point-js.html">Point</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Symbolizer/Polygon-js.html">Polygon</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Symbolizer/Raster-js.html">Raster</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Symbolizer/Text-js.html">Text</a></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent33')">Tile</a><div class=MGroupContent id=MGroupContent33><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Tile-js.html">Tile</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Tile/Image-js.html">Image</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Tile/Image/IFrame-js.html">Image.<wbr>IFrame</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Tile/UTFGrid-js.html">UTFGrid</a></div></div></div></div></div><div class=MEntry><div class=MFile><a href="../files/deprecated-js.html">Deprecated</a></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent34')">OpenLayers</a><div class=MGroupContent id=MGroupContent34><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Console-js.html">Console</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Events-js.html">Events</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Icon-js.html">Icon</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Kinetic-js.html">Kinetic</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Map-js.html">Map</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Projection-js.html">Projection</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Rule-js.html">Rule</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/SingleFile-js.html">SingleFile.js</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Style-js.html">Style</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Style2-js.html">Style2</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/StyleMap-js.html">StyleMap</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Tween-js.html">Tween</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Util-js.html">Util</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Spherical-js.html">Spherical</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Animation-js.html">Animation</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Events/buttonclick-js.html">Events.<wbr>buttonclick</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Events/featureclick-js.html">Events.<wbr>featureclick</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/TileManager-js.html">TileManager</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/Util/vendorPrefix-js.html">Util.<wbr>vendorPrefix</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/WPSClient-js.html">WPSClient</a></div></div><div class=MEntry><div class=MFile><a href="../files/OpenLayers/WPSProcess-js.html">WPSProcess</a></div></div></div></div></div></div></div></div><div class=MEntry><div class=MGroup><a href="javascript:ToggleMenu('MGroupContent36')">Index</a><div class=MGroupContent id=MGroupContent36><div class=MEntry><div class=MIndex id=MSelected>Everything</div></div><div class=MEntry><div class=MIndex><a href="Classes.html">Classes</a></div></div><div class=MEntry><div class=MIndex><a href="Constants.html">Constants</a></div></div><div class=MEntry><div class=MIndex><a href="Functions.html">Functions</a></div></div><div class=MEntry><div class=MIndex><a href="Properties.html">Properties</a></div></div><div class=MEntry><div class=MIndex><a href="Files.html">Files</a></div></div><div class=MEntry><div class=MIndex><a href="Constructor.html">Constructor</a></div></div></div></div></div><script type="text/javascript"><!--
var searchPanel = new SearchPanel("searchPanel", "HTML", "../search");
--></script><div id=MSearchPanel class=MSearchPanelInactive><input type=text id=MSearchField value=Search onFocus="searchPanel.OnSearchFieldFocus(true)" onBlur="searchPanel.OnSearchFieldFocus(false)" onKeyUp="searchPanel.OnSearchFieldChange()"><select id=MSearchType onFocus="searchPanel.OnSearchTypeFocus(true)" onBlur="searchPanel.OnSearchTypeFocus(false)" onChange="searchPanel.OnSearchTypeChange()"><option  id=MSearchEverything selected value="General">Everything</option><option value="Classes">Classes</option><option value="Constants">Constants</option><option value="Constructor">Constructor</option><option value="Files">Files</option><option value="Functions">Functions</option><option value="Properties">Properties</option></select></div><script language=JavaScript><!--
HideAllBut([36], 37);// --></script></div><!--Menu-->


<div id=MSearchResultsWindow><iframe src="" frameborder=0 name=MSearchResults id=MSearchResults></iframe><a href="javascript:searchPanel.CloseResultsWindow()" id=MSearchResultsWindowClose>Close</a></div>


<script language=JavaScript><!--
if (browserType) {if (browserVer) {document.write("</div>"); }document.write("</div>");}// --></script></body></html>