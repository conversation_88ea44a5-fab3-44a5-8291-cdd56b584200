var map;
var markersLayer;

function initialize() {
  map = new OpenLayers.Map("map");
  layer = new OpenLayers.Layer.OSM();
  layer.tileOptions.crossOriginKeyword = null;
  map.addLayer(layer);

  var lat = 32.75;
  var lon = -97.13;
  var zoom = 8;

  var fromProjection = new OpenLayers.Projection("EPSG:4326"); // Transform from WGS 1984
  var toProjection = new OpenLayers.Projection("EPSG:900913"); // to Spherical Mercator Projection
  var position = new OpenLayers.LonLat(lon, lat).transform(
    fromProjection,
    toProjection
  );
  map.setCenter(position, zoom);

  // Initialize markers layer
  markersLayer = new OpenLayers.Layer.Markers("Markers");
  map.addLayer(markersLayer);

  map.events.register("click", map, function (e) {
    var lonlat = map.getLonLatFromViewPortPx(e.xy);
    var transformed = lonlat.transform(
        map.getProjectionObject(),
        new OpenLayers.Projection("EPSG:4326")
    );
    var lon = transformed.lon;
    var lat = transformed.lat;
    // Remove previous marker if any
    markersLayer.clearMarkers();

    // Add new marker
    var marker = new OpenLayers.Marker(lonlat);
    markersLayer.addMarker(marker);
    sendRequest(lat, lon);
  });
}

function sendRequest(lat, lon) {
  var xhr = new XMLHttpRequest();
  xhr.open("GET", "proxy.php?lat=" + lat + "&lon=" + lon);
  //    console.log(lat, lon);
  xhr.setRequestHeader("Accept", "application/json");
  xhr.onreadystatechange = function () {
    if (this.readyState == 4 && this.status == 200) {
      var json = JSON.parse(this.responseText);
      console.log(json);
      if (json.cod !== 200 || !json.sys || !json.weather || !json.main) {
        document.getElementById("output").innerHTML =
          "<strong>Error:</strong> Could not retrieve weather data.";
        console.error("Unexpected response:", json);
        return;
      }
      var weatherData = `
                <h3>Weather Information</h3>
                <strong>Location:</strong> ${json.name}, ${json.sys.country}<br>
                <strong>Description:</strong> ${json.weather[0].description}<br>
                <strong>Temperature:</strong> ${(
                  json.main.temp - 273.15
                ).toFixed(2)} °C<br>  <!-- Convert K to C -->
                <strong>Temperature (F):</strong> ${(
                  ((json.main.temp - 273.15) * 9) / 5 +
                  32
                ).toFixed(2)} °F<br> <!-- Convert K to F -->
                <strong>Humidity:</strong> ${json.main.humidity}%<br>
                <strong>Wind Speed:</strong> ${json.wind.speed} m/s
            `;
      document.getElementById("output").innerHTML = weatherData;
    }
  };
  xhr.send(null);
}
