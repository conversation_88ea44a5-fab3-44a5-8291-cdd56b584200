var map;
var markersLayer;

function initialize() {
  map = new OpenLayers.Map("map");
  layer = new OpenLayers.Layer.OSM();
  layer.tileOptions.crossOriginKeyword = null;
  map.addLayer(layer);

  var lat = 32.75;
  var lon = -97.13;
  var zoom = 8;

  var fromProjection = new OpenLayers.Projection("EPSG:4326"); // Transform from WGS 1984
  var toProjection = new OpenLayers.Projection("EPSG:900913"); // to Spherical Mercator Projection
  var position = new OpenLayers.LonLat(lon, lat).transform(
    fromProjection,
    toProjection
  );
  map.setCenter(position, zoom);

  // Initialize markers layer
  markersLayer = new OpenLayers.Layer.Markers("Markers");
  map.addLayer(markersLayer);

  map.events.register("click", map, function (e) {
    console.log("Map clicked!"); // Debug log

    var lonlat = map.getLonLatFromViewPortPx(e.xy);
    console.log("Original lonlat:", lonlat.lon, lonlat.lat); // Debug log

    // Transform coordinates to WGS84 for the API call
    var transformed = lonlat.clone().transform(
        map.getProjectionObject(),
        new OpenLayers.Projection("EPSG:4326")
    );
    var lon = transformed.lon;
    var lat = transformed.lat;
    console.log("Transformed coordinates:", lat, lon); // Debug log

    // Remove and recreate markers layer to avoid DOM issues
    if (markersLayer) {
      map.removeLayer(markersLayer);
    }
    markersLayer = new OpenLayers.Layer.Markers("Markers");
    map.addLayer(markersLayer);

    // Add new marker at the clicked location
    var marker = new OpenLayers.Marker(lonlat);
    markersLayer.addMarker(marker);

    // Send weather request with transformed coordinates
    sendRequest(lat, lon);
  });
}

function sendRequest(lat, lon) {
  console.log("Sending weather request for:", lat, lon); // Debug log
  var xhr = new XMLHttpRequest();
  xhr.open("GET", "proxy.php?lat=" + lat + "&lon=" + lon);
  xhr.setRequestHeader("Accept", "application/json");
  xhr.onreadystatechange = function () {
    if (this.readyState == 4) {
      console.log("Response status:", this.status); // Debug log
      if (this.status == 200) {
        var json = JSON.parse(this.responseText);
        console.log("Weather data received:", json);
        if (json.cod !== 200 || !json.sys || !json.weather || !json.main) {
          document.getElementById("output").innerHTML =
            "<strong>Error:</strong> Could not retrieve weather data.";
          console.error("Unexpected response:", json);
          return;
        }
      var weatherData = `
                <h3>Weather Information</h3>
                <strong>Location:</strong> ${json.name}, ${json.sys.country}<br>
                <strong>Description:</strong> ${json.weather[0].description}<br>
                <strong>Temperature:</strong> ${(
                  json.main.temp - 273.15
                ).toFixed(2)} °C<br>  <!-- Convert K to C -->
                <strong>Temperature (F):</strong> ${(
                  ((json.main.temp - 273.15) * 9) / 5 +
                  32
                ).toFixed(2)} °F<br> <!-- Convert K to F -->
                <strong>Humidity:</strong> ${json.main.humidity}%<br>
                <strong>Wind Speed:</strong> ${json.wind.speed} m/s
            `;
        document.getElementById("output").innerHTML = weatherData;
      } else {
        document.getElementById("output").innerHTML =
          "<strong>Error:</strong> Failed to fetch weather data. Status: " + this.status;
        console.error("Request failed with status:", this.status);
      }
    }
  };
  xhr.send(null);
}
